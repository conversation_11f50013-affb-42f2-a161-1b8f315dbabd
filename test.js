const message = [
  {
    choices: [{ index: 0, delta: { content: '', type: 'text' } }],
    model: '',
    prompt_token_usage: 97,
    chunk_token_usage: 0,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '好的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '让我', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '介绍一下', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '首先', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '我需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '确定', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '对', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的了解', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '程度', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '他们', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '听说过', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '但', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '想', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '深入了解', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '或者', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '他们', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '刚开始', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '学', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '前端', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '想', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '选择一个', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '框架', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '是一个', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '流行的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'JavaScript', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '库', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '所以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应该', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '从', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '基础', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开始', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '讲', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '它的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用途', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '核心', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '概念', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '接下来', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '想知道', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的优势', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '在哪里', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '为什么', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '选择', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '而不是', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '其他', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '框架', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '这时候', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提到', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虚拟', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'DOM', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '化', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '单向', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '数据', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '流', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '这些', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '特点', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '还要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比较', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '一下', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'V', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ue', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '或', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Ang', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ular', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '但', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '没有', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '所以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '暂时', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '不需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '深入', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比较', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '然后', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '核心', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '概念', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '部分', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'JS', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'X', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '状态', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '属性', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '这些', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '是', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '必须', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '解释', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '清楚', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '函数', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '类', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '件的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '区别', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '不过', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '现在', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'H', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ooks', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '更', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '流行', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应该', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '重点', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '介绍', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'H', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ooks', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'use', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'State', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'use', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Effect', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '生命周期', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '方法', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虽然', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '重要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '但', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '函数', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '加上', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'H', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ooks', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '已经', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '逐渐', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '成为', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '主流', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '所以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '简要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提及', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '还需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提到', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的应用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '场景', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '单', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '页面', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '或者', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '跨', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '平台', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Native', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '对', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '实际', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '例子', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '感兴趣', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '哪些', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '大', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '公司在', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Facebook', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Instagram', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '这', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '增加', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可信', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '度', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '另外', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '生态', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '方面', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '周边', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '工具', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Router', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Red', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ux', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Next', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.js', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '介绍', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '但', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '不要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '太过', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '深入', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '保持', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '概述', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '即可', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '想', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '了解', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '学习', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '哪些', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '前置', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '知识', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'JavaScript', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ES', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '6', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的基础', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '所以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应该', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提到', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '这点', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '还要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '注意', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '避免', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '使用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '太多', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '技术', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '术语', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '或者', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '尽量', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '解释', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '清楚', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虚拟', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'DOM', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '简单', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '说明', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '它的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '作用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提升', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '性能', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'JS', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'X', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可能需要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '解释', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '为什么', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '在', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'JavaScript', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '中', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '写', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '类似', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'HTML', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '代码', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '\n\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '最后', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '确保', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '结构', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '清晰', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '分', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '点', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '列出', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '让', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '容易', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '阅读', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '检查', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '有没有', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '遗漏', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的重要', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '点', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '比如', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '声明', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '式', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '编程', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '模式', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '或者', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '它的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '社区', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '支持', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '总结', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '部分', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可以', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '强调', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '流行', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '程度', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '适用', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '性', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '鼓励', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '学习', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '\n', type: 'thinking' } }],
    model: '',
    chunk_token_usage: 0,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
    thinking_elapsed_secs: 18,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '是一个', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '由', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Facebook', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '现', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Meta', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开源', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' JavaScript', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '库', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用于', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '构建', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用户', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '界面', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'UI', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '），', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '尤其', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '擅长', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '高效', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '动态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的单', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '页面', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'SP', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'A', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）。', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '它的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '核心', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '思想', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '是通过', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '化', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '将', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '复杂的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' UI', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '拆', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '分为', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '独立', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '且', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '复', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '代码', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '片段', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提升', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '效率和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '维护', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '性', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '---\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '###', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的核心', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '特点', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '1', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '化', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  \n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' UI', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '被', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '拆', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '分为', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '独立', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '如', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '按钮', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '表单', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '页面', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '区块', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '），', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '每个', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '管理', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '自身的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '逻辑', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '样式', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '支持', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '函数', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '使用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' H', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ooks', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '类', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '两种', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '形式', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '示例', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '一个', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '按钮', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可以', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '复', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用在', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '多个', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '页面', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '中', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '2', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虚拟', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' DOM', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Virtual', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' DOM', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  \n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '通过', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虚拟', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' DOM', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '优化', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '性能', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '先在', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '内存', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '中', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '构建', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虚拟', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' DOM', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '树', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '仅', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '更新', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '实际', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' DOM', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '中', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '变化', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的部分', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '避免', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '全', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '量', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '渲染', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '3', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'JS', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'X', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '语法', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  \n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '允许', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '在', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' JavaScript', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '中', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '直接', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '编写', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '类似', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' HTML', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的结构', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '使', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '代码', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '更', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '直观', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '示例', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'const', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' element', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' =', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' <', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'h', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '1', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Hello', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ',', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '!</', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'h', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '1', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ';', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '4', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '单向', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '数据', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '流', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Props', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' &', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' State', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  \n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Props', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '父', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '向', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '子', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '传递', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '数据', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '只', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '读', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'State', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '内部', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '管理的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '状态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '通过', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' `', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'use', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'State', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '函数', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '或', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' `', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'this', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.state', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '类', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '更新', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '5', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'H', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ooks', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '函数', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '件的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '增强', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  \n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '从', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '16', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '8', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '引入', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '允许', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '函数', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '使用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '状态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '生命周期', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '等', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '特性', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '常用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' H', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ooks', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'use', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'State', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`,', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' `', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'use', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Effect', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`,', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' `', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'use', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Context', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '等', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '6', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '生态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '丰富', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  \n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '  ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' -', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '周边', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '工具', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '链', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '完善', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '路由', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Router', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '状态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '管理', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Red', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ux', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'M', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ob', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'X', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Context', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' API', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '服务', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '端', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '渲染', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '框架', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Next', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '.js', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '移动', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '端', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Native', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '等', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '---\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '###', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '为什么', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '选择', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '？', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '高效', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '灵活', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '化和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '虚拟', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' DOM', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '提升', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '性能', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '适合', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '复杂', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' UI', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '社区', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '强大', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Meta', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '维护', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '拥有', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '庞大', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发者', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '社区', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '第三方', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '库', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '支持', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '跨', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '平台', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Native', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '支持', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '同一', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '套', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '代码', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' iOS', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '/', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Android', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '渐进', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '式', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '学习', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '：', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '基础', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '概念', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '简单', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '逐步', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '掌握', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '高级', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '功能', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '---\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '###', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '一个', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '简单', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '示例', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '```', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'js', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'x', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'import', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ',', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' {', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' useState', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' }', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' from', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: " '", type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'react', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: "';\n\n", type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'function', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Counter', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '()', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' {\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' const', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' [', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ',', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' set', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ']', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' =', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' useState', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '(', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '0', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ');', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' //', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '使用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' useState', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Hook', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' return', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' (\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '   ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' <', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'div', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '     ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' <', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'p', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ':', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' {', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '}</', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'p', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '     ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' <', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'button', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' onClick', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '={()', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' =>', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' set', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'Count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '(count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' +', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '1', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ')}', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '+', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '1', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '</', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'button', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '   ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' </', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'div', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '>\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' );\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '}\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'export', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' default', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' Counter', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ';\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '```\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '这是一个', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '计数器', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '点击', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '按钮', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '时', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '更新', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '状态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'count', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '`', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）。\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '---\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '###', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '适用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '场景', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '单', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '页面', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '应用', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'SP', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'A', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '动态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '数据', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '驱动的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' UI', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '如', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '社交', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '平台', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '仪表', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '盘', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '需要', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '高', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '交互', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '性的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '网页', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '如', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '在线', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '编辑器', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '、', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '实时', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '聊天', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '---\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '###', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '学习', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '前提', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '基础', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' JavaScript', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '（', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'ES', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '6', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '+', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '）\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' HTML', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '/C', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'SS', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '知识', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '-', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '了解', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' npm', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '/y', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'arn', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '等', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '包', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '管理', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '工具', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '---\n\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '###', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' **', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '总结', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '**\n', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '通过', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '组件', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '化和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '声明', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '式', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '编程', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '帮助', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发者', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '高效', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '构建', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '可', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '维护', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' UI', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '其', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '活跃', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '生态', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '和', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '跨', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '平台', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '能力', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '使其', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '成为', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '现代', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '前端', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '开发', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '的主流', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '选择', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '。', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '如果你是', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '前端', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '新手', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '，', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: 'React', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: ' ', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '是', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '值得', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '深入', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '学习的', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '工具', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '之一', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },

  {
    choices: [{ index: 0, delta: { content: '！', type: 'text' } }],
    model: '',
    chunk_token_usage: 1,
    created: 1741161199,
    message_id: 2,
    parent_id: 1,
  },
]
export default message
