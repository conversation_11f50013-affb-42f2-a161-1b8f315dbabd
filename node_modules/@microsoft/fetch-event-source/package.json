{"_from": "@microsoft/fetch-event-source", "_id": "@microsoft/fetch-event-source@2.0.1", "_inBundle": false, "_integrity": "sha512-W6CLUJ2eBMw3Rec70qrsEW0jOm/3twwJv21mrmj2yORiaVmVYGS4sSS5yUwvQc1ZlDLYGPnClVWmUUMagKNsfA==", "_location": "/@microsoft/fetch-event-source", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@microsoft/fetch-event-source", "name": "@microsoft/fetch-event-source", "escapedName": "@microsoft%2ffetch-event-source", "scope": "@microsoft", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/@microsoft/fetch-event-source/-/fetch-event-source-2.0.1.tgz", "_shasum": "9ceecc94b49fbaa15666e38ae8587f64acce007d", "_spec": "@microsoft/fetch-event-source", "_where": "D:\\Project\\deep-seek", "author": {"name": "Microsoft"}, "bugs": {"url": "https://github.com/Azure/fetch-event-source/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A better API for making Event Source requests, with all the features of fetch()", "devDependencies": {"@types/jasmine": "^3.6.9", "jasmine": "^3.7.0", "nyc": "^15.1.0", "rimraf": "^3.0.2", "source-map-support": "^0.5.19", "typescript": "^4.2.4"}, "homepage": "https://github.com/Azure/fetch-event-source#readme", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "name": "@microsoft/fetch-event-source", "repository": {"type": "git", "url": "git+https://github.com/Azure/fetch-event-source.git"}, "scripts": {"build": "tsc && tsc -p tsconfig.esm.json", "clean": "rimraf ./lib ./coverage", "prebuild": "npm run clean", "prepublishOnly": "npm run build && npm run test", "test": "nyc jasmine --config=jasmine.json"}, "sideEffects": false, "types": "lib/cjs/index.d.ts", "version": "2.0.1"}