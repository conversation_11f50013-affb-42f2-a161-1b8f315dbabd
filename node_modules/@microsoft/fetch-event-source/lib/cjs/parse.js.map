{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../../src/parse.ts"], "names": [], "mappings": ";;;AAqBO,KAAK,UAAU,QAAQ,CAAC,MAAkC,EAAE,OAAkC;IACjG,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAClC,IAAI,MAAmD,CAAC;IACxD,OAAO,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;QACzC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACzB;AACL,CAAC;AAND,4BAMC;AAeD,SAAgB,QAAQ,CAAC,MAAuD;IAC5E,IAAI,MAA8B,CAAC;IACnC,IAAI,QAAgB,CAAC;IACrB,IAAI,WAAmB,CAAC;IACxB,IAAI,sBAAsB,GAAG,KAAK,CAAC;IAGnC,OAAO,SAAS,OAAO,CAAC,GAAe;QACnC,IAAI,MAAM,KAAK,SAAS,EAAE;YACtB,MAAM,GAAG,GAAG,CAAC;YACb,QAAQ,GAAG,CAAC,CAAC;YACb,WAAW,GAAG,CAAC,CAAC,CAAC;SACpB;aAAM;YAEH,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAChC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;QAChC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,OAAO,QAAQ,GAAG,SAAS,EAAE;YACzB,IAAI,sBAAsB,EAAE;gBACxB,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAyB,EAAE;oBAC3C,SAAS,GAAG,EAAE,QAAQ,CAAC;iBAC1B;gBAED,sBAAsB,GAAG,KAAK,CAAC;aAClC;YAGD,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;YACjB,OAAO,QAAQ,GAAG,SAAS,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE;gBACvD,QAAQ,MAAM,CAAC,QAAQ,CAAC,EAAE;oBACtB;wBACI,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;4BACpB,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;yBACtC;wBACD,MAAM;oBAEV;wBACI,sBAAsB,GAAG,IAAI,CAAC;oBAClC;wBACI,OAAO,GAAG,QAAQ,CAAC;wBACnB,MAAM;iBACb;aACJ;YAED,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;gBAGhB,MAAM;aACT;YAGD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,WAAW,CAAC,CAAC;YACzD,SAAS,GAAG,QAAQ,CAAC;YACrB,WAAW,GAAG,CAAC,CAAC,CAAC;SACpB;QAED,IAAI,SAAS,KAAK,SAAS,EAAE;YACzB,MAAM,GAAG,SAAS,CAAC;SACtB;aAAM,IAAI,SAAS,KAAK,CAAC,EAAE;YAGxB,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACpC,QAAQ,IAAI,SAAS,CAAC;SACzB;IACL,CAAC,CAAA;AACL,CAAC;AAnED,4BAmEC;AASD,SAAgB,WAAW,CACvB,IAA0B,EAC1B,OAAgC,EAChC,SAA6C;IAE7C,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;IAC3B,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAGlC,OAAO,SAAS,MAAM,CAAC,IAAgB,EAAE,WAAmB;QACxD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAEnB,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAG,OAAO,CAAC,CAAC;YACrB,OAAO,GAAG,UAAU,EAAE,CAAC;SAC1B;aAAM,IAAI,WAAW,GAAG,CAAC,EAAE;YAGxB,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YAC5D,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,OAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzF,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;YAEzD,QAAQ,KAAK,EAAE;gBACX,KAAK,MAAM;oBAGP,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;wBACvB,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK;wBAC7B,CAAC,CAAC,KAAK,CAAC;oBACZ,MAAM;gBACV,KAAK,OAAO;oBACR,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;oBACtB,MAAM;gBACV,KAAK,IAAI;oBACL,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC;oBACzB,MAAM;gBACV,KAAK,OAAO;oBACR,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACf,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;qBAClC;oBACD,MAAM;aACb;SACJ;IACL,CAAC,CAAA;AACL,CAAC;AA5CD,kCA4CC;AAED,SAAS,MAAM,CAAC,CAAa,EAAE,CAAa;IACxC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IAChD,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACX,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;IACrB,OAAO,GAAG,CAAC;AACf,CAAC;AAED,SAAS,UAAU;IAKf,OAAO;QACH,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,EAAE;QACT,EAAE,EAAE,EAAE;QACN,KAAK,EAAE,SAAS;KACnB,CAAC;AACN,CAAC"}