{"_from": "event-source-polyfill", "_id": "event-source-polyfill@1.0.31", "_inBundle": false, "_integrity": "sha512-4IJSItgS/41IxN5UVAVuAyczwZF7ZIEsM1XAoUzIHA6A+xzusEZUutdXz2Nr+MQPLxfTiCvqE79/C8HT8fKFvA==", "_location": "/event-source-polyfill", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "event-source-polyfill", "name": "event-source-polyfill", "escapedName": "event-source-polyfill", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/event-source-polyfill/-/event-source-polyfill-1.0.31.tgz", "_shasum": "45fb0a6fc1375b2ba597361ba4287ffec5bf2e0c", "_spec": "event-source-polyfill", "_where": "D:\\Project\\deep-seek", "author": {"name": "Yaffle", "url": "https://github.com/Yaffle"}, "bugs": {"url": "https://github.com/Yaffle/EventSource/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A polyfill for http://www.w3.org/TR/eventsource/ ", "devDependencies": {"grunt": "^1.4.1", "grunt-cli": "^1.4.3", "grunt-contrib-uglify": "^5.0.1"}, "files": ["src/*"], "homepage": "https://github.com/Yaffle/EventSource", "keywords": ["sse", "server sent events", "eventsource", "event-source", "polyfill"], "license": "MIT", "main": "src/eventsource.js", "name": "event-source-polyfill", "repository": {"type": "git", "url": "git://github.com/Yaffle/EventSource.git"}, "scripts": {"build": "grunt"}, "version": "1.0.31"}