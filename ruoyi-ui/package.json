{"name": "ruoyi", "version": "3.8.9", "description": "DeepSeek内网体验版", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@riophae/vue-treeselect": "0.4.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.0.0", "@vue-office/pdf": "^1.0.0", "@vue-office/pptx": "^1.0.1", "@vue/composition-api": "^1.7.2", "axios": "0.28.1", "clipboard": "2.0.8", "core-js": "3.37.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "5.4.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-toc-done-right": "^4.2.0", "nanoid": "^5.1.3", "nprogress": "0.2.0", "quill": "2.0.2", "screenfull": "5.0.2", "sortablejs": "1.10.2", "splitpanes": "2.4.1", "ua-parser-js": "^2.0.2", "v-clipboard": "^3.0.0-next.1", "vconsole": "^3.15.1", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.10", "vue-markdown": "^2.2.4", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-touch": "^1.1.0", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@fortaine/fetch-event-source": "^3.0.6", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "^4.0.0", "lamejs": "^1.2.1", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "2.6.12", "webpack-bundle-analyzer": "^4.10.2"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}