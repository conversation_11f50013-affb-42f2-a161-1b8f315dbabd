!(function() { var e; try { var t = null; function Xe(e) { return Xe = Object.setPrototypeOf ? Object.getPrototypeOf : function(e) { return e.__proto__ || Object.getPrototypeOf(e) }, Xe(e) } function Ke() { try { var e = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function() {})) } catch (e) {} return (Ke = function() { return !!e })() } function Ye(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e } function $e(e) { return e && typeof Symbol !== 'undefined' && e.constructor === Symbol ? 'symbol' : typeof e } function Ge(e, t) { return !t || $e(t) !== 'object' && typeof t !== 'function' ? Ye(e) : t } function Qe(e, t, n) { return t = Xe(t), Ge(e, Ke() ? Reflect.construct(t, n || [], Xe(e).constructor) : t.apply(e, n)) } function Ze(e, t) { if (!(e instanceof t)) throw new TypeError('Cannot call a class as a function') } function et(e, t) { for (var n = 0; n < t.length; n++) { var r = t[n]; r.enumerable = r.enumerable || !1, r.configurable = !0, 'value' in r && (r.writable = !0), Object.defineProperty(e, r.key, r) } } function tt(e, t, n) { return t && et(e.prototype, t), n && et(e, n), e } function nt(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } function rt(e, t) { return rt = Object.setPrototypeOf || function(e, t) { return e.__proto__ = t, e }, rt(e, t) } function ot(e, t) { if (typeof t !== 'function' && t !== null) throw new TypeError('Super expression must either be null or a function'); e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: !0, configurable: !0 }}), t && rt(e, t) } function it(e, t, n) { return it = Ke() ? Reflect.construct : function(e, t, n) { var r = [null]; r.push.apply(r, t); var o = new (Function.bind.apply(e, r))(); return n && rt(o, n.prototype), o }, it.apply(null, arguments) } function at(e) { var n = typeof t === 'function' ? new t() : void 0; return at = function(e) { if (e === null || (t = e, Function.toString.call(t).indexOf('[native code]') === -1)) return e; var t; if (typeof e !== 'function') throw new TypeError('Super expression must either be null or a function'); if (void 0 !== n) { if (n.has(e)) return n.get(e); n.set(e, r) } function r() { return it(e, arguments, Xe(this).constructor) } return r.prototype = Object.create(e.prototype, { constructor: { value: r, enumerable: !1, writable: !0, configurable: !0 }}), rt(r, e) }, at(e) } function ct(e) { for (var t = 1; t < arguments.length; t++) { var n = arguments[t] != null ? arguments[t] : {}; var r = Object.keys(n); typeof Object.getOwnPropertySymbols === 'function' && (r = r.concat(Object.getOwnPropertySymbols(n).filter(function(e) { return Object.getOwnPropertyDescriptor(n, e).enumerable }))), r.forEach(function(t) { nt(e, t, n[t]) }) } return e } function ut(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var r = Object.getOwnPropertySymbols(e); t && (r = r.filter(function(t) { return Object.getOwnPropertyDescriptor(e, t).enumerable })), n.push.apply(n, r) } return n } var n = Object.prototype.toString; function st(e) { return e && (void 0 === e ? 'undefined' : $e(e)) === 'object' ? 'stack' in e && typeof e.stack === 'string' ? lt(e) : ft(e) ? JSON.stringify(e) : String(e) : String(e) } function lt(e) { var t = String(e); var n = e.stack || ''; return n.indexOf(t) > -1 ? n : ''.concat(t, '\n').concat(n) } function ft(e) { if (!e || (void 0 === e ? 'undefined' : $e(e)) !== 'object') return !1; if (n.call(e) !== '[object Object]') return !1; var t = Object.getPrototypeOf(e); return t == null || Object.getPrototypeOf(t) == null } function dt(e) { var t; var n; var r; var o; var i = []; var a = (r = ct({}, e.bean), o = (o = { sessionId: ((t = e.bean) === null || void 0 === t ? void 0 : t.sessionId) || Math.random().toString(36).slice(2) }) != null ? o : {}, Object.getOwnPropertyDescriptors ? Object.defineProperties(r, Object.getOwnPropertyDescriptors(o)) : ut(Object(o)).forEach(function(e) { Object.defineProperty(r, e, Object.getOwnPropertyDescriptor(o, e)) }), r); function c() { n || (n = setTimeout(u, 10)) } function u() { if (n = void 0, i.length !== 0) { var t = e.from; typeof t === 'function' && (t = t()), t == null && (t = location.origin || ''.concat(location.protocol, '//').concat(location.host)); var r = ''.concat('https://aegis.qq.com/collect', '?id=').concat(e.id, '&from=').concat(t); a.aid && (r += '&aid='.concat(encodeURIComponent(a.aid))), a.sessionId && (r += '&sessionId='.concat(encodeURIComponent(a.sessionId))), a.uin && (r += '&uin='.concat(encodeURIComponent(a.uin))), a.version && (r += '&version='.concat(encodeURIComponent(a.version))), a.ext1 && (r += '&ext1='.concat(encodeURIComponent(a.ext1))), a.ext2 && (r += '&ext2='.concat(encodeURIComponent(a.ext2))), a.ext3 && (r += '&ext3='.concat(encodeURIComponent(a.ext3))), (function(e, t) { var n = new XMLHttpRequest(); 'withCredentials' in n || (n = new XDomainRequest()); for (var r = [], o = t.length, i = 0; i < o; i++)r.push(['msg['.concat(i, ']=').concat(encodeURIComponent(t[i].msg)), 'level['.concat(i, ']=').concat(t[i].level)].join('&')); r.push('count='.concat(o)), n.open('POST', e), n.setRequestHeader('Content-type', 'application/x-www-form-urlencoded'), n.send(r.join('&')) }(r, i.reverse())), i = [] } } return { bean: a, setBean: function(e) { e.sessionId && (a.sessionId = e.sessionId), e.aid && (a.aid = e.aid), e.uin && (a.uin = e.uin) }, info: function(e) { i.unshift({ level: 2, msg: st(e) }) }, warn: function(e) { i.unshift({ level: 2, msg: st(e) }) }, error: function(e) { i.unshift({ level: 4, msg: st(e) }), c() }, enqueueFlush: c, flush: u } } var r = window.encodeURIComponent; function pt(e) { return e } function vt() { return Math.random().toString(36).slice(2) } var o = pt('250401-115224-143'); var i = pt('wwopendata.web'); var a = pt(''); var c = pt(''); var u = pt('m9ck2xv1.aJCG9RUWxKwsDchdEsFkgepk0LUTp220Mwf30vRqW5it'); var s = pt(''.concat(i, '@').concat(o)); var l = dt({ id: 'bRLDot6R4Kymzz0jPO', bean: { sessionId: u, version: s }}); var f = l.info; var d = l.warn; var p = l.error; var v = l.flush; var g = {}; function gt(e, t) { ht(e, function e(n) { wt('name', e), t(n) }) } function ht(e, t) { mt(g, e) || (g[e] = []), g[e].push(t) } function wt(e, t) { if (mt(g, e)) { var n = g[e]; var r = n.indexOf(t); r >= 0 && n.splice(r, 1) } } function yt(e, t) { var n = arguments.length > 2 && void 0 !== arguments[2] && arguments[2]; if (mt(g, e)) if (n) { var r = { type: e, detail: t }; g[e].forEach(function(t) { try { t(r) } catch (t) { console.log('[event] '.concat(e, ' error: ').concat(t)) } }) } else yt(e, t, !0) } function mt(e, t) { return Object.prototype.hasOwnProperty.call(e, t) } function bt(e, t) { return function() { for (var n = arguments.length, r = new Array(n), o = 0; o < n; o++)r[o] = arguments[o]; try { return t.apply(this, r) } catch (t) { St(t, e), yt('error', t) } } }e = { captureException: St }; var h = jt(f); var w = jt(d); var y = jt(p); function Ot(e) { f(e), v() } function St(e, t) { p('['.concat(t, '] ').concat(Et(e))) } function jt(e) { return function() { for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++)n[r] = arguments[r]; for (var o = [], i = 0, a = n.length; i < a; i++)o.push(Ct(n[i])); e(o.join(' ')) } } function Ct(e) { return e ? (void 0 === e ? 'undefined' : $e(e)) !== 'object' ? e : typeof e.stack === 'string' ? Et(e) : JSON.stringify(e) : e } function Et(e) { return ''.concat(e, ' ').concat(e.stack) } var m = Object.freeze({ __proto__: null, captureException: St, captureMessage: Ot, error: y, log: h, warn: w, wrap: bt }); function At(e, t) { (t == null || t > e.length) && (t = e.length); for (var n = 0, r = new Array(t); n < t; n++)r[n] = e[n]; return r } function kt(e) { if (Array.isArray(e)) return At(e) } function _t(e) { if (typeof Symbol !== 'undefined' && e[Symbol.iterator] != null || e['@@iterator'] != null) return Array.from(e) } function xt() { throw new TypeError('Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.') } function Dt(e, t) { if (e) { if (typeof e === 'string') return At(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); return n === 'Object' && e.constructor && (n = e.constructor.name), n === 'Map' || n === 'Set' ? Array.from(n) : n === 'Arguments' || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? At(e, t) : void 0 } } function Mt(e) { return kt(e) || _t(e) || Dt(e) || xt() } var b = '[ww-open-data]'; function It() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)t[n] = arguments[n] } function Pt() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)t[n] = arguments[n]; var r; (r = m).log.apply(r, Mt(t)) } function Tt() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)t[n] = arguments[n]; var r; (r = m).warn.apply(r, Mt(t)) } function Rt() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)t[n] = arguments[n]; var r; (r = m).error.apply(r, Mt(t)) } function Ft() { for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)t[n] = arguments[n]; var r, o; (r = console).error.apply(r, [b].concat(Mt(t))), (o = m).error.apply(o, Mt(t)) } var O = Function.prototype.call; function Wt() { var e = { Math: window.Math, Number: window.Number, JSON: window.JSON, Promise: window.Promise, Uint8Array: window.Uint8Array, Function: window.Function, Object: window.Object, Array: window.Array, String: window.String, WeakMap: window.WeakMap, Element: window.Element, ShadowRoot: window.ShadowRoot, Image: window.Image, Node: window.Node, EventTarget: window.EventTarget, HTMLIFrameElement: window.HTMLIFrameElement, CanvasRenderingContext2D: window.CanvasRenderingContext2D }; var t = { fetch: window.fetch, parseInt: window.parseInt, setTimeout: window.setTimeout }; var n = Object.create(null); return qt(n, 'protected', Object.create(null)), qt(n, 'singleton', Object.create(null)), Object.keys(e).forEach(function(t) { e[t] && (qt(n.singleton, t, e[t]), qt(n.protected, t, Nt(e[t])), e[t].prototype && (qt(n.protected[t], 'prototype', Nt(e[t].prototype)), Object.freeze(n.protected[t].prototype)), Object.freeze(n.protected[t])) }), Object.keys(t).forEach(function(e) { qt(n.singleton, e, t[e]) }), qt(n.singleton, 'call', O.bind(O)), Object.defineProperty(n, '__version__', { value: s }), Object.freeze(n.protected), Object.freeze(n.singleton), Object.freeze(n), n } function Nt(e) { var t = Object.create(null); return Object.getOwnPropertyNames(e).forEach(function(n) { if (n !== 'prototype') { var r = Object.getOwnPropertyDescriptor(e, n); var o = Object.create(null); qt(o, 'value', r.value), qt(o, 'get', r.get), qt(o, 'set', r.set), qt(t, n, Object.freeze(o)) } }), t } function qt(e, t, n) { Object.defineProperty(e, t, { value: n, enumerable: !0 }) } var S = Wt(); var j = S == null ? void 0 : S.protected; var C = S == null ? void 0 : S.singleton; var E = (C == null ? void 0 : C.call) || Function.prototype.call.bind(Function.prototype.call); var A = Ut(j, 'Function.prototype.bind'); var k = Ut(j, 'Function.prototype.call'); var _ = function(e) { return e && E(A, k, e) }; Ut(j, 'Object.keys'); var x = Ut(j, 'Object.defineProperty'); var D = Ut(C, 'setTimeout', 'direct'); var M = _(Ut(j, 'Object.prototype.hasOwnProperty')); var I = _(Ut(j, 'Array.prototype.push')); var P = _(Ut(j, 'Array.prototype.forEach')); var T = _(Ut(j, 'Array.prototype.slice')); _(Ut(j, 'Math.random')), _(Ut(j, 'Number.prototype.toString')); var R = _(Ut(j, 'String.prototype.indexOf')); var F = _(Ut(j, 'String.prototype.slice')); var W = Ut(C, 'Image', 'direct'); var N = _(Ut(j, 'Image.prototype.src', 'set')) || _(Ut(j, 'HTMLImageElement.prototype.src', 'set')) || function(e, t) { e.src = t }; var q = _(Ut(j, 'HTMLIframeElement.prototype.contentWindow', 'get')) || function(e) { return e.contentWindow }; var U = _(Ut(j, 'HTMLIframeElement.prototype.contentDocument', 'get')) || function(e) { return e.contentDocument }; var L = _(Ut(j, 'EventTarget.prototype.addEventListener')) || function(e, t, n) { e.addEventListener(t, n) }; var z = _(Ut(j, 'CanvasRenderingContext2D.prototype.fillText')); var J = _(Ut(j, 'CanvasRenderingContext2D.prototype.drawImage')); var H = _(Ut(j, 'CanvasRenderingContext2D.prototype.strokeText')); var B = _(Ut(j, 'CanvasRenderingContext2D.prototype.measureText')); var V = Ut(C, 'WeakMap', 'direct'); var X = _(Ut(j, 'WeakMap.prototype.get')); var K = _(Ut(j, 'WeakMap.prototype.set')); var Y = _(Ut(j, 'Element.prototype.attachShadow')); var $ = _(Ut(j, 'Node.prototype.textContent', 'set')); function Ut(e, t) { var n; var r; var o; var i; var a; var c = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 'value'; var u = (j == null || (n = j.String) === null || void 0 === n ? void 0 : n.prototype.split.value) || window.String.prototype.split; var s = (j == null || (r = j.Array) === null || void 0 === r ? void 0 : r.prototype.pop.value) || window.Array.prototype.pop; var l = (j == null || (o = j.Array) === null || void 0 === o ? void 0 : o.prototype.forEach.value) || window.Array.prototype.forEach; var f = (j == null || (i = j.Object) === null || void 0 === i ? void 0 : i.getOwnPropertyDescriptor.value) || Object.getOwnPropertyDescriptor; var d = E(u, t, '.'); var p = E(s, d); var v = e; var g = window; if (E(l, d, function(e) { v && (v = v[e]), g && (g = g[e]) }), v == null ? void 0 : v[p]) { if (v = v[p], c === 'direct') return v; if (v[c]) return v[c] } if (g) return c === 'direct' || c === 'value' ? g[p] : (a = f(g, p)) === null || void 0 === a ? void 0 : a[c] } var G = new W(); var Q = !1; var Z = null; var ee = []; function Lt(e) { Q ? e() : Z ? e(Z) : ee.push(e) } function zt() { var e = ee; ee = [], P(e, function(e) { e(Z) }) } function Jt(e, t) { if (M(e, t)) return e[t] } function Ht(e, t, n) { x(e, t, { value: n, writable: !0, enumerable: !0, configurable: !0 }) }L(G, 'load', function() { Q = !0, D(zt, 1) }), L(G, 'error', function() { Z = new Error('Failed to load crossorigin image'), D(zt, 1) }), N(G, 'https://wwcdn.weixin.qq.com/node/wework/images/1x1-00000000.91e42db1c6.png'); var te = '\ufeff'; var ne = String.fromCharCode(8204); var re = String.fromCharCode(8205); var oe = String.fromCharCode(8203); var ie = [String.fromCharCode(8206), String.fromCharCode(8207), ne, re, oe]; var ae = {}; var ce = 0; function Bt(e) { var t = Jt(e, 'encrypt_token'); if (t) return t; var n = Jt(e, 'data'); var r = Jt(e, 'encrypt_text_data'); if (n && r) { for (var o = (ce++).toString(5), i = '', a = 0, c = o.length; a < c; a++)i += ie[Number(o[a])]; var u = te + r + i + te; return Ht(ae, u, n), Ht(e, 'encrypt_token', u), u } } function Vt(e) { var t = R(e, te, 0); if (t === -1) return e; for (var n = F(e, 0, t); t !== -1;) { var r = R(e, te, t + 1); if (r === -1) break; var o = Jt(ae, F(e, t, r + 1)); o ? (n += o, t = r + 1) : (n += F(e, t, r), t = r) } return n + F(e, t) } var ue = V && new V(); var se = !1; var le = !1; function Xt() { var e; if (se) return !0; var t = (e = window.CanvasRenderingContext2D) === null || void 0 === e ? void 0 : e.prototype; return !!t && (t.strokeText = function(e, t, n, r) { if (!Q) return r == null ? H(this, e, t, n) : H(this, e, t, n, r); var o = Vt(e); return o !== e && $t(this), r == null ? H(this, o, t, n) : H(this, o, t, n, r) }, t.fillText = function(e, t, n, r) { if (!Q) return r == null ? z(this, e, t, n) : z(this, e, t, n, r); var o = Vt(e); return o !== e && $t(this), r == null ? z(this, o, t, n) : z(this, o, t, n, r) }, t.measureText = function(e) { return B(this, Q ? Vt(e) : e) }, se = !0, !0) } function Kt() { Tt('enable canvas sharing'), le = !0 } function Yt() { le = !1 } function $t(e) { X(ue, e) || (le || J(e, G, 0, 0), K(ue, e, !0)) } var fe = {}; function Gt(e, t, n) { return ''.concat(e, '::').concat(t, '::').concat(n || '') } function Qt(e) { return Gt(Jt(e, 'type'), Jt(e, 'id'), Jt(e, 'corpid')) } function Zt(e) { return Jt(fe, e) } var de = vt(); var pe = 1; var ve = {}; var ge = {}; var he = {}; function en(e, t) { var n = ''.concat(de, '.').concat(pe++); var r = []; var o = {}; var i = 0; if (P(e, function(e) { var t = Jt(e, 'type'); var a = Jt(e, 'id'); if (t && a) { var c = Gt(t, a, Jt(e, 'corpid')); Zt(c) || M(o, c) || (Ht(o, c, !0), i += 1, M(ve, c) ? I(Jt(ve, c), n) : (Ht(ve, c, [n]), I(r, e))) } }), i) return Ht(he, n, i), Ht(ge, n, t), r; t() } function tn(e, t) { if (!e) return Rt('[callback] missing response'), void nn(t, new Error('missing response data')); var n = Jt(e, 'items'); if (!n) return Rt('[callback] missing items'), void nn(t, new Error('missing response items')); P(n, function(e) { var t; t = Qt(e), Ht(fe, t, e) }); var r = []; P(t, function(e) { var t = Qt(e); if (Zt(t)) { var n = Jt(ve, t); n && (delete ve[t], P(n, function(e) { var t = Jt(ge, e); if (t) { var n = Jt(he, e) - 1; n > 0 ? Ht(he, e, n) : (delete he[e], delete ge[e], t()) } })) } else I(r, e) }), r.length > 0 && nn(r, new Error('missing items')) } function nn(e, t) { P(e, function(e) { var n = Qt(e); var r = Jt(ve, n); r && (delete ve[n], P(r, function(e) { var n = Jt(ge, e); delete he[e], delete ge[e], n && n(t) })) }) } var we = location.origin || ''.concat(location.protocol, '//').concat(location.host); var ye = 'https://open.work.weixin.qq.com'; var me = ''.concat(ye, '/wwopen/openData/frame/index#origin=').concat(r(we)); function rn(e) { for (var t = Ut(j, 'JSON.parse'), n = Ut(C, 'Uint8Array', 'direct'), r = Ut(j, 'String.fromCodePoint'), o = r, i = new n(e), a = '', c = 0, u = i.length; c < u;) { var s = i[c++]; if (s <= 127)a += o(s); else { var l = 63 & i[c++]; if (s <= 223)a += o((31 & s) << 6 | l); else { var f = 63 & i[c++]; if (s <= 239)a += o((15 & s) << 12 | l << 6 | f); else { var d = 63 & i[c++]; r ? a += o((7 & s) << 18 | l << 12 | f << 6 | d) : (a += o(63), c += 3) } } } } return t(a) } function on(e) { for (var t = Ut(C, 'Uint8Array', 'direct'), n = Ut(C, 'parseInt', 'direct'), r = _(Ut(j, 'String.prototype.substring')), o = new t(e.length / 2), i = 0, a = e.length; i < a; i += 2)o[i / 2] = n(r(e, i, i + 2), 16); return o } var be; var Oe; var Se; var je = { skey: null }; var Ce = ((be = window.crypto) === null || void 0 === be ? void 0 : be.subtle) || ((Oe = window.crypto) === null || void 0 === Oe ? void 0 : Oe.webkitSubtle); function an(e, t, n, r) { un(e, t, n, r, !0) } function cn(e, t, n, r) { un(e, t, n, r, !1) } function un(e, t, n, r) { var o = arguments.length > 4 && void 0 !== arguments[4] && arguments[4]; if (window.fetch)window.fetch(e, { method: 'POST', credentials: 'include', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(t) }).then(function(e) { if (e.status === 200) return o ? e.arrayBuffer() : e.json(); var t = 'Invalid response status '.concat(e.status); return e.json().catch(function() { throw new Error(t) }).then(function(e) { throw (e == null ? void 0 : e.result) ? e == null ? void 0 : e.result : new Error(t) }) }).then(n, r); else { var i = new XMLHttpRequest(); i.open('POST', e), i.withCredentials = !0, o && (i.responseType = 'arraybuffer'), i.setRequestHeader('Content-Type', 'application/json'), i.onreadystatechange = function() { if (i.readyState === XMLHttpRequest.DONE) if (i.status === 200) if (o)n(i.response); else try { n(JSON.parse(i.responseText)) } catch (e) { r(new Error('Parse response error')) } else r(new Error('Invalid response status '.concat(i.status))) }, i.onerror = function() { r(new Error('Request error')) }, i.send(JSON.stringify(t)) } } function sn(e) { if (!a) return Rt('[fetchData] missing referer'), void nn(e, { errMsg: 'wwapp.fetchOpenData:fail', hint: 'Missing referer for jwxwork.js. See: https://developer.work.weixin.qq.com/document/path/91958' }); var t = vt(); var n = ''.concat(ye, '/wwopen/openData/getOpenData?f=json&r=').concat(t); Pt('[fetchData] begin #'.concat(t)); var r = Se || (Se = Ce.importKey('raw', on(a), 'AES-CBC', !1, ['decrypt'])); function o(n) { Rt('[fetchData] fetch fail #'.concat(t, ' (').concat(t, ')'), n), nn(e, { errMsg: 'wwapp.fetchOpenData:fail', rand: t, detail: n }) }an(n, { items: e, skey: je.skey, sid: c }, function(n) { Pt('[fetchData] fetch res #'.concat(t)), r.then(function(e) { return Ce.decrypt({ name: 'AES-CBC', iv: new Uint8Array(16) }, e, n) }).then(rn).then(function(n) { Pt('[fetchData] fetch parsed #'.concat(t)), tn(n, e) }).catch(o) }, o) } function ln(e, t, n) { var r = null; try { r = U(e) } catch (e) {} if (r !== null) throw new Error('Missing cross origin'); It('[iframe] postMessage', e, t, n), q(e).postMessage(t, n) } var Ee = {}; var Ae = vt(); var ke = 0; function fn(e) { var t = ''.concat(Ae, '.').concat(vt(), '.').concat(ke++); return Ht(Ee, t, e), t } function dn(e) { delete Ee[e] }vt(); var _e; var xe = 1e3; var De = 20; var Me = []; function pn(e, t) { var n = en(e, t); n && (P(n, function(e) { I(Me, e) }), !_e && Me.length && (_e = D(bt('flush-fetch', vn), De))) } function vn() { var e = Me; _e = void 0, Me = []; for (var t = e.length, n = 0; n < t; n += xe) { sn(T(e, n, n + xe)) } } var Ie = (function() { function e() { var t = this; Ze(this, e), nt(this, 'iframe', document.createElement('iframe')), nt(this, 'state', 1), nt(this, 'queue', []), nt(this, 'timer', void 0), this.iframe.onload = bt('MainFrame.onload', function() { Pt('[MainFrame] onload'), t.state = 2, t.fetchData() }), this.iframe.onerror = function(e) { St((e == null ? void 0 : e.error) || new Error('MainFrame load error'), 'MainFrame.onerror'), t.state = 3 }, this.iframe.style.display = 'none', this.iframe.referrerPolicy = 'origin', this.iframe.src = me } return tt(e, [{ key: 'enqueueFetch', value: function(e) { var t = this; this.queue.push(e), this.timer || (this.timer = D(bt('MainFrame.timeout', function() { t.state === 2 && t.fetchData() }), 20)) } }, { key: 'fetchData', value: function() { var e = {}; var t = []; this.queue.forEach(function(n) { var r = ''.concat(n.type, '::').concat(n.id, '::').concat(n.corpid || ''); e[r] || (e[r] = !0, t.push(n)) }), t.length && (this.timer = void 0, this.queue = [], Pt('[MainFrame] fetchData'), ln(this.iframe, JSON.stringify({ type: 'fetch', items: t, skey: je.skey, sid: c }), ye)) } }]), e }()); var Pe = ['fontFamily', 'fontSize', 'fontWeight', 'fontStyle', 'fontVariant', 'fontStretch', 'fontSizeAdjust', 'color', 'cursor']; var Te = (function() { function e(t, n) { Ze(this, e), nt(this, 'container', void 0), nt(this, 'mainFrame', void 0), nt(this, 'renderType', void 0), nt(this, 'loadState', void 0), nt(this, 'renderEl', void 0), this.container = t, this.mainFrame = n, this.renderType = 1, this.loadState = 1 } return tt(e, [{ key: 'update', value: function() { var e = this.getItem(); if (!e.type || !e.id) return this.renderEmpty(); this.renderText(e) } }, { key: 'renderEmpty', value: function() { this.setChild(1) } }, { key: 'renderText', value: function(e) { var t = this; if (this.renderType !== 2 || this.loadState === 3) { var n = document.createElement('iframe'); n.onload = bt('Frame.onload', function() { t.renderEl === n && (t.loadState = 2, t.notifyUpdate()) }), n.onerror = function(e) { t.renderEl === n && (St((e == null ? void 0 : e.error) || new Error('Frame load error'), 'Frame.onerror'), t.loadState = 3) }; var o = r(''.concat(e.type, '::').concat(e.id, '::').concat(e.corpid || '')); n.frameBorder = '0', n.referrerPolicy = 'origin', n.src = ''.concat(me, '&init=').concat(o), this.loadState = 4, this.setChild(2, n) } this.mainFrame.enqueueFetch(e), this.loadState === 2 && this.notifyUpdate(e) } }, { key: 'setChild', value: function(e, t) { for (var n = this.container; n.firstChild;)n.removeChild(n.firstChild); t && n.appendChild(t), this.renderEl = t, this.renderType = e } }, { key: 'getItem', value: function() { return { type: this.container.getAttribute('type'), id: this.container.getAttribute('openid'), corpid: this.container.getAttribute('corpid') || void 0 } } }, { key: 'notifyUpdate', value: function() { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.getItem(); if (e.type && e.id) { var t = {}; var n = getComputedStyle(this.container); Pe.forEach(function(e) { t[e] = n[e] }), ln(this.renderEl, JSON.stringify({ type: 'update', item: e, style: t }), ye) } } }]), e }()); var Re = { 'ww.opendata.event': function(e, t) { var n; e.eventType === 'click' && ((n = t.parentNode) === null || void 0 === n || n.click()) }, 'ww.opendata.resize': function(e, t) { var n = e.size; t.style.width = n.width, t.style.height = n.height, yt('update', { el: t.parentNode, hasData: !!n.width }) } }; function gn(e) { if (e.origin === ye) { var t; try { t = JSON.parse(e.data) } catch (e) {}t && Re[t.type] && P(document.querySelectorAll('ww-open-data iframe'), function(n) { n.contentWindow === e.source && Re[t.type](t, n) }) } } var Fe; var We; var Ne = '__WW_OPENDATA_RENDER__'; function hn() { window.addEventListener ? window.addEventListener('message', bt('dispatchMessage', gn)) : window.attachEvent('onmessage', bt('dispatchMessage', gn)); var e = document.querySelector('head'); We = document.createElement('style'), e.appendChild(We), Fe = new Ie(), e.appendChild(Fe.iframe); var t = We.sheet; t.insertRule('ww-open-data { display: inline-block; vertical-align: text-bottom; overflow: hidden }', 0), t.insertRule('ww-open-data img { display: block; width: 100%; height: 100% }', 1), t.insertRule('ww-open-data iframe { display: block; width: 0; height: 0 }', 2) } function wn(e) { if (e) { var t = e; t[Ne] || (t[Ne] = new Te(e, Fe)), t[Ne].update() } } function yn(e) { P(e, wn) } var qe = V && new V(); function mn(e) { var t = X(qe, e); if (t) return t; try { var n = Y(e, { mode: 'closed' }); return K(qe, e, n), n } catch (t) { Rt('[getShadow] fail', e, t) } } function bn(e) { It('[bindAll] begin', e), P(e, On), It('[bindAll] end') } function On(e) { if (!Sn(e, !0)) { je.skey || (yt('error', { errMsg: 'bind:fail', message: 'missing agentConfig', element: e }), console.error('[ww-open-data] 页面未完成 wx.agentConfig，请先完成 wx.agentConfig 再调用 WWOpenData.bind，后续将逐步下线未完成 wx.agentConfig 时调用 WWOpenData.bind 的支持')); var t = vt(); pn([{ type: e.getAttribute('type'), id: e.getAttribute('openid'), corpid: e.getAttribute('corpid') }], bt('bind-pending', function(n) { if (n) return Rt('[bind] fetch error', t, n), void yt('error', { errMsg: 'bind:fail', message: 'fetch open-data fail', detail: n }); Sn(e) })) } } function Sn(e) { var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1]; if (!(e == null ? void 0 : e.getAttribute)) return yt('error', { errMsg: 'bind:fail', message: 'missing bind element', element: e }), !1; var n = e.getAttribute('type'); if (!n) return yt('error', { errMsg: 'bind:fail', message: 'missing open-data type', element: e }), !1; var r = e.getAttribute('openid'); if (!r) return yt('error', { errMsg: 'bind:fail', message: 'missing open-data openid', element: e }), !1; var o = e.getAttribute('corpid'); var i = mn(e); if (!i) return yt('error', { errMsg: 'bind:fail', message: 'attach shadow fail', element: e }), !1; var a = Zt(Gt(n, r, o)); if (!a) return t || yt('error', { errMsg: 'bind:fail', message: 'missing open-data item', element: e }), !1; var c = Jt(a, 'data'); return $(i, c || ''), yt('update', { element: e, hasData: !!c }), !!c } var Ue = 0; Y || (Ue |= 1), Ce || (Ue |= 2), document.location.protocol === 'http:' && (Ue |= 4); var Le; var ze = 0; var Je = navigator.userAgent; if (!(/miniProgram/i.test(Je) || window.__wxjs_environment === 'miniprogram'))/wxwork/i.test(Je) && (ze |= 4), window.WeixinSandBox && (ze |= 1), ((Le = window.wx) === null || void 0 === Le ? void 0 : Le.agentConfig) && (ze |= 2); var He = bt('bind', Ue ? wn : On); var Be = bt('bindAll', Ue ? yn : bn); function jn() { En('checkSession', { sid: c }, arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}) } function Cn() { var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}; if (!(e.corpid && e.agentid && e.timestamp && e.nonceStr && e.signature && e.jsApiList)) { var t; var n; var r = { err_Info: 'fail', errMsg: 'agentConfig:fail', hint: 'Missing params' }; return (t = e.fail) === null || void 0 === t || t.call(e, r), void ((n = e.complete) === null || void 0 === n || n.call(e, r)) } var o = { corpid: ''.concat(e.corpid), agentid: ''.concat(e.agentid), timestamp: ''.concat(e.timestamp), nonceStr: ''.concat(e.nonceStr), signature: ''.concat(e.signature), jsApiList: e.jsApiList, url: location.href }; En('agentConfig', { config: o, sid: c }, e, function(e) { var t, n; je.skey && ((t = e.data) === null || void 0 === t ? void 0 : t.skey) !== je.skey && (fe = {}), je.skey = (n = e.data) === null || void 0 === n ? void 0 : n.skey, Be(document.querySelectorAll('ww-open-data')), Pt('[user config] #'.concat(JSON.stringify(o))) }) } function En(e, t) { var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}; var r = arguments.length > 3 ? arguments[3] : void 0; var o = vt(); Pt('[invoke] '.concat(e, ' begin #').concat(o)), cn(''.concat(ye, '/wwopen/openData/').concat(e, '?f=json&r=').concat(o), t, function(t) { var i; var a; var c; var u; var s = ((i = t.data) === null || void 0 === i ? void 0 : i.result) || t.data || { errMsg: ''.concat(e, ':fail') }; s.errMsg === ''.concat(e, ':ok') ? (It('[invoke] '.concat(e, ' succ #').concat(o)), r == null || r(t), (c = n.success) === null || void 0 === c || c.call(n, s)) : (Rt('[invoke] '.concat(e, ' fail #').concat(o), t), (u = n.fail) === null || void 0 === u || u.call(n, s)); (a = n.complete) === null || void 0 === a || a.call(n, s) }, function(t) { var r, i; Rt('[invoke] '.concat(e, ' fail #').concat(o), t); var a = { errMsg: ''.concat(e, ':fail') }; (r = n.fail) === null || void 0 === r || r.call(n, a), (i = n.complete) === null || void 0 === i || i.call(n, a) }) } function An(e, t) { var n = e.items; pn(n, function(e) { if (e) return t(e); var r = []; P(n, function(e) { var t = Zt(Qt(e)); t && Jt(t, 'datakind') === 1 && r.push({ type: e.type, id: e.id, corpid: e.corpid, data: Bt(t) }) }), Lt(function(e) { e ? t(e) : t(null, { items: r }) }) }) } function kn(e, t) { t(null, function(e) { _n(e) }) } function _n(e, t) { ln(e, { type: 'ww-open-data:inject-session', data: t }, arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : '*') } if (ze)Tt('skip inject', ze); else { if (Ue ? (Tt('inject iframe', Ue), hn()) : Pt('inject begin', Ue), a || Ft('Missing referer for jwxwork.js. See: https://work.weixin.qq.com/api/doc/90001/90143/91958'), window.wx ? window.wx.agentConfig || Pt('define wx.agentConfig') : Tt('missing window.wx'), window.wx || (window.wx = {}), window.wx.agentConfig || x(window.wx, 'agentConfig', { value: bt('agentConfig', Cn) }), window.WWOpenData)Rt('window.WWOpenData already exists'); else { var Ve = {}; xn(Ve, 'bindAll', Be), xn(Ve, 'bind', He), xn(Ve, 'on', ht), xn(Ve, 'once', gt), xn(Ve, 'off', wt), xn(Ve, 'checkSession', jn), xn(Ve, 'initCanvas', Xt), xn(Ve, 'enableCanvasSharing', Kt), xn(Ve, 'disableCanvasSharing', Yt), xn(Ve, 'prefetch', An), xn(Ve, 'registerOpenFrame', bt('registerOpenFrame', fn)), xn(Ve, 'unregisterOpenFrame', bt('unregisterOpenFrame', dn)), xn(Ve, 'createOpenSessionInjector', bt('createOpenSessionInjector', kn)), x(Ve, '__version__', { value: s }), xn(Ve, 'agentConfig', bt('agentConfig', Cn)), xn(window, 'WWOpenData', Ve), Pt('window.WWOpenData defined', window.WWOpenData) }'customElements' in window && !customElements.get('ww-open-data') && Dn() } function xn(e, t, n) { x(e, t, { value: n, enumerable: !0 }) } function Dn() { try { Pt('register custom element'); var e = function(e) { e._current = { type: e.getAttribute('type'), id: e.getAttribute('openid'), corpid: e.getAttribute('corpid') }, e._current.type && e._current.id && je.skey && He(e) }; var t = (function(t) { function n() { var t; return Ze(this, n), nt(t = Qe(this, n), '_current', {}), t.getAttribute('type') && t.getAttribute('openid') ? (e(t), t) : Ge(t) } return ot(n, t), tt(n, [{ key: 'attributeChangedCallback', value: function() { this._current.type === this.getAttribute('type') && this._current.id === this.getAttribute('openid') && this._current.corpid === this.getAttribute('corpid') || e(this) } }], [{ key: 'observedAttributes', get: function() { return ['type', 'openid', 'corpid'] } }]), n }(at(HTMLElement))); customElements.define('ww-open-data', t) } catch (e) { St(e, 'register custom element') } } } catch (Z) { e.captureException(Z, '?') } }())
