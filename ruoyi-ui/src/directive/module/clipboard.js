/**
* v-clipboard 文字复制剪贴
* Copyright (c) 2021 ruoyi
*/

import Clipboard from 'clipboard'
import { debounce } from '@/utils/index'

export default {
  bind(el, binding, vnode) {
    // 获取防抖延迟时间，默认为300ms
    const debounceTime = parseInt(binding.modifiers.debounce || 300)

    switch (binding.arg) {
      case 'success':
        // 使用防抖包装成功回调
        el._vClipBoard_success = binding.value ? debounce(binding.value, debounceTime, false) : binding.value
        break
      case 'error':
        // 使用防抖包装错误回调
        el._vClipBoard_error = binding.value ? debounce(binding.value, debounceTime, false) : binding.value
        break
      default: {
        const clipboard = new Clipboard(el, {
          text: () => binding.value,
          action: () => binding.arg === 'cut' ? 'cut' : 'copy'
        })
        clipboard.on('success', e => {
          const callback = el._vClipBoard_success
          callback && callback(e)
        })
        clipboard.on('error', e => {
          const callback = el._vClipBoard_error
          callback && callback(e)
        })
        el._vClipBoard = clipboard
      }
    }
  },
  update(el, binding) {
    // 获取防抖延迟时间，默认为300ms
    const debounceTime = parseInt(binding.modifiers.debounce || 300)

    if (binding.arg === 'success') {
      el._vClipBoard_success = binding.value ? debounce(binding.value, debounceTime, false) : binding.value
    } else if (binding.arg === 'error') {
      el._vClipBoard_error = binding.value ? debounce(binding.value, debounceTime, false) : binding.value
    } else {
      el._vClipBoard.text = function() { return binding.value }
      el._vClipBoard.action = () => binding.arg === 'cut' ? 'cut' : 'copy'
    }
  },
  unbind(el, binding) {
    if (!el._vClipboard) return
    if (binding.arg === 'success') {
      delete el._vClipBoard_success
    } else if (binding.arg === 'error') {
      delete el._vClipBoard_error
    } else {
      el._vClipBoard.destroy()
      delete el._vClipBoard
    }
  }
}
