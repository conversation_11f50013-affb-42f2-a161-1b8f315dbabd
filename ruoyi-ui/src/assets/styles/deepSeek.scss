@for $i from 1 through 200 {
  .margin-#{$i} {
    margin: 1px * $i;
  }
  .margin-top-#{$i} {
    margin-top: 1px * $i;
  }
  .margin-bottom-#{$i} {
    margin-bottom: 1px * $i;
  }
  .margin-tb-#{$i} {
    margin-top: 1px * $i;
    margin-bottom: 1px * $i;
  }
  .margin-lr-#{$i} {
    margin-left: 1px * $i;
    margin-right: 1px * $i;
  }
  .margin-left-#{$i} {
    margin-left: 1px * $i;
  }
  .margin-right-#{$i} {
    margin-right: 1px * $i;
  }

  .padding-#{$i} {
    padding: 1px * $i;
  }
  .padding-top-#{$i} {
    padding-top: 1px * $i;
  }
  .padding-bottom-#{$i} {
    padding-bottom: 1px * $i;
  }
  .padding-tb-#{$i} {
    padding-top: 1px * $i;
    padding-bottom: 1px * $i;
  }
  .padding-lr-#{$i} {
    padding-left: 1px * $i;
    padding-right: 1px * $i;
  }
  .padding-left-#{$i} {
    padding-left: 1px * $i;
  }
  .padding-right-#{$i} {
    padding-right: 1px * $i;
  }
  .text-#{$i} {
    font-size: 1px * $i;
  }
}


.self-blue {
  color: #3386FF;
}

.radius {
  border-radius: 5px;
}

.text-right {
  text-align: right;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}


.h100 {
            height: 100%;
}

.flex{
            display:flex;
            align-items: center;
}

.flex-center {
            justify-content: center;
}

.flex-between {
            justify-content: space-between;
}

.flex-end {
            justify-content: flex-end;
}

.flex-align-end{
            align-items: flex-end;
}
