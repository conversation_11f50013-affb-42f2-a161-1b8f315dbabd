import request from '@/utils/request'

// 用户连续登录天数和总积分接口
export const userbehavior = params => {
  return request({
    url: '/api/userbehavior/one',
    method: 'post',
    data: params
  })
}
// 获取积分-积分任务列表
export const tasklist = params => {
  return request({
    url: '/api/pointstask/tasklist',
    method: 'post',
    data: params
  })
}
// 获取积分-积分(z明细
export const pointsdetails = (params) => {
  return request({
    url: '/api/pointsdetails/page/user',
    method: 'post',
    data: params
  })
}
// 排行榜-今日
export const pointsdetailsTodayRank = params => {
  return request({
    url: '/api/pointsdetails/today/rank',
    method: 'post',
    data: params
  })
}
// 排行榜-本周
export const pointsdetailsWeekRank = params => {
  return request({
    url: '/api/pointsdetails/week/rank',
    method: 'post',
    data: params
  })
}
// 排行榜-本月
export const pointsdetailsMonthRank = params => {
  return request({
    url: '/api/pointsdetails/month/rank',
    method: 'post',
    data: params
  })
}

// 用户姓名
export const getSysuser = params => {
  return request({
    url: '/api/sysuser/info',
    method: 'post',
    data: params
  })
}
