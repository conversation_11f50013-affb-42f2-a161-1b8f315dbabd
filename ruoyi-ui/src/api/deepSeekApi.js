import request from '@/utils/request'
import { getToken } from '@/utils/auth'

// 取初始化数据
export const getInitData = (params) => {
  return request({
    url: '/api/common/system/getInitData',
    method: 'get',
    params
  })
}
export const getHistories = (params) => {
  return request({
    url: '/api/core/chat/getHistories',
    method: 'post',
    data: params
  })
}
export const chatInit = (params) => {
  return request({
    url: '/api/core/chat/init',
    method: 'get',
    data: params,
    params
  })
}
// 电投壹初始化会话
export const chatInit1 = (params) => {
  return request({
    url: '/api/core/chat/outLink/init',
    method: 'get',
    data: params,
    params
  })
}
// 取得对话内容记录(分页)
export const getPaginationRecords = (params) => {
  return request({
    url: '/api/core/chat/getPaginationRecords',
    method: 'post',
    data: params
  })
}
// 电投壹授权1
export const getSignature = (params) => {
  return request({
    // url: '/api/wechat-signature',
    url: '/api/getSignature',
    method: 'get',
    data: params,
    params
  })
}
// 电投壹授权2
export const getAgentTicket = (params) => {
  return request({
    url: '/api/getAgent',
    method: 'get',
    data: params,
    params
  })
}
// 用户回答点赞和点踩
export const updateUserFeedback = (params) => {
  return request({
    url: '/api/core/chat/feedback/updateUserFeedback',
    method: 'post',
    data: params
  })
}
// 取得上下文/详情
export const getResData = (params) => {
  return request({
    url: '/api/core/chat/getResData',
    method: 'get',
    data: params,
    params
  })
}
// 删除提问
export const itemDelete = (params) => {
  return request({
    url: '/api/core/chat/item/delete',
    method: 'delete',
    data: params,
    params
  })
}
// 上传文件
export const uploadFile = (params) => {
  return request({
    url: '/api/common/file/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params
  })
}
// 发起提问
// export const completions = async(params) => {
//   const token = getToken() || sessionStorage.getItem('token') || localStorage.getItem('token')
//   console.log('completions token', token)
//   const headers = new Headers({
//     Authorization:
//       'Bearer ' + token,
//     'Content-Type': 'application/json'
//   })

//   const response = await fetch('/dev-api/api/v1/chat/completions', {
//     method: 'post',
//     headers: headers,
//     body: JSON.stringify(params)
//   })

//   if (!response.ok) {
//     throw new Error(`HTTP error! status: ${response.status}`)
//   }

//   return response
// }
// 修改对话标题对话置顶
export const updateHistory = (params) => {
  return request({
    url: '/api/core/chat/updateHistory',
    method: 'put',
    data: params
  })
}
// 删除对话
export const delHistory = (params) => {
  return request({
    url: '/api/core/chat/delHistory',
    method: 'delete',
    data: params,
    params
  })
}
// 删除对话
export const transcriptions = (params) => {
  return request({
    url: '/api/v1/audio/transcriptions',
    // url: '/api/v1/audio/test',
    headers: {
      //   Authorization: 'Bearer fastgpt-hQraWtL6gi3TgF9thJx03UTMxiIiVjPXJocZMkO58Or1yxPO4XlLef',
      //   'smart-proxy-key': '75904E7331F60799F5F6C147B58D92EB',
      'Content-Type': 'multipart/form-data'
    },
    method: 'post',
    data: params
    // params
  })
}

// export const outLinkUidInit = (params) => {
//   return request({
//     url: '/api/core/chat/outLink/init',
//     headers: {
//       Authorization:
//         'Bearer fastgpt-hQraWtL6gi3TgF9thJx03UTMxiIiVjPXJocZMkO58Or1yxPO4XlLef',
//       'Content-Type': 'multipart/form-data'
//     },
//     method: 'get',
//     params
//   })
// }
export const clearHistories = (params) => {
  return request({
    url: '/api/core/chat/clearHistories',
    method: 'delete',
    params
  })
}

export const getAccessToken = (params) => {
  return request({
    url: 'https://elink.spic.com.cn/sns/oauth2/access_token',
    method: 'get',
    params
  })
}

export const getUserInfo = (params) => {
  return request({
    url: '/api/getUserInfo',
    method: 'get',
    params
  })
}
export const getModelList = (params) => {
  return request({
    url: '/api/qacontent/list',
    method: 'post',
    data: params,
    params
  })
}

// export const ttsWav = params => {
//   return request({
//     url: '/tts',
//     method: 'post',
//     headers: {
//       // 可选，如果全局已配置可省略
//       'Content-Type': 'application/json'
//     },
//     data: params
//   })
// }

export const downloadWavFile = (params) => {
  return request({
    url: '/tts/ttsToDownlaod',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/**
 * 获取文件及excel文件下载路径
 * @param {*} params
 * @returns
 */
export const filedownload = (params) => {
  return request({
    url: '/api/common/mobile/filedownload',
    method: 'post',
    data: params
  })
}
/**
 * 获取文件及音频文件下载路径
 * @param {*} params
 * @returns
 */
export const getMobileFile = (params) => {
  return request({
    url: '/tts/ttsToDownlaodPreviewFile',
    method: 'post',
    data: params
  })
}

/**
 * 获取是否有读取引用文件查看权限
 * @param {*} params
 * @returns
 */
export const getReadFilePower = (params) => {
  return request({
    url: '/api/core/dataset/data/getPermission',
    method: 'get',
    params
  })
}

/**
 * 查看引用文件内容
 * @param {*} params
 * @returns
 */
export const lookFile = (params) => {
  return request({
    url: '/api/core/chat/getCollectionQuote',
    method: 'post',
    data: params
  })
}
/**
 * 查看引用文件的全部引用
 * @param {*} params
 * @returns
 */
export const getQuote = (params) => {
  return request({
    url: '/api/core/chat/getQuote',
    method: 'post',
    data: params
  })
}

/**
 * 获取文件大小及数据流
 * @param {*} params
 * @returns
 */
export const getFileSize = (params) => {
  return request({
    url: '/api/common/file/read',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/**
 * 获取office文件大小及url
 * @param {*} params
 * @returns
 */
export const getOfficeSize = (params) => {
  return request({
    url: '/api/common/file/getFileSize',
    method: 'post',
    data: params
  })
}

/**
 * 获取内网用户信息
 * @param {*} params
 * @returns
 */
export const getUser = (params) => {
  return request({
    url: '/system/user/getInfo',
    method: 'get',
    params
  })
}

/**
 * 对用户信息加密
 * @param {*} params
 * @returns
 */
export const getAddEncryptUserInfo = (params) => {
  return request({
    url: '/api/common/getWebUserId',
    method: 'post',
    data: params
  })
}

/**
 * 是否有通知消息
 * @param {*} params
 * @returns
 */
export const getNotifyList = (params) => {
  return request({
    url: '/api/notice/list',
    method: 'post',
    data: params
  })
}

/**
 * 时间段内是否有消息
 * @param {*} params
 * @returns
 */
export const qaversionusageContent = (params) => {
  return request({
    url: '/api/qaversionusage/content',
    method: 'get',
    params
  })
}

/**
 * 系统联网信息
 * @param {*} params
 * @returns
 */
export const searchfuncInfo = (params) => {
  return request({
    url: '/api/searchfunc/info',
    method: 'get',
    params
  })
}

/**
 * 引入文件 鼠标移动上去展示的片段
 * @param {*} params
 * @returns
 */
export const getQuoteData = (params) => {
  return request({
    url: '/api/core/chat/getQuoteData',
    method: 'post',
    data: params
  })
}
/**
 * 知识库列表信息
 * @param {*} params
 * @returns
 */
// ... existing code ...
export const groupKnowledgeList = (params) => {
  return request({
    url: 'api/core/knowledge/type',
    method: 'get',
    params
  })
}
/**
 * 当前用户有权限的知识库
 * @returns
 */
export const getPermissionKnowledge = (params) => {
  return request({
    url: '/api/knowledge/user/permission',
    method: 'get',
    params
  })
}
