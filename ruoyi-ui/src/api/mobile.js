import request from '@/utils/request'

// 用户连续登录天数和总积分接口
export const getTotal = (params) => {
  return request({
    url: '/api/userbehavior/one',
    method: 'post',
    params
  })
}

// 获取 用户姓名信息
export const getUserNameRequest = (staffId) => {
  return request({
    url: '/api/sysuser/info/' + staffId,
    method: 'get'
  })
}

// 获取积分任务列表
export const getIntegralTaskListRequest = (staffId) => {
  return request({
    url: '/api/pointstask/tasklist/' + staffId,
    method: 'get'
  })
}

// 获取积分明细
export const getIntegralDetailRequest = (staffId, data) => {
  return request({
    url: '/api/pointsdetails/page/' + staffId,
    method: 'post',
    data
  })
}

// 获取 排行榜-今日
export const getRankingListRequest = (time, staffId) => {
  return request({
    url: `/api/pointsdetails/${time}/rank`,
    method: 'post',
    data: staffId
  })
}

// 上传图片接口
export const uploadImgRequest = (fileMd5, data) => {
  return request({
    url: `/api/userfeedback/uploadPicture?fileMd5=${fileMd5}`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 提交 反馈表单接口
export const feedbackRequest = (data) => {
  return request({
    url: `/api/userfeedback/add`,
    method: 'post',
    data
  })
}

// 个人用户反馈分页查询
export const getFeedbackListRequest = (data) => {
  return request({
    url: `/api/userfeedback/page/user`,
    method: 'post',
    data
  })
}

//获取互动总次数
export const getInteractionTotalRequest = () => {
  return request({
    url: `/api/usersession/sessionnum`,
    method: 'get'
  })
}
