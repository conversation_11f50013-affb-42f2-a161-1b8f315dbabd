import TextMsgClass from '@/views/TextMsgClass'
import TextCompletions from '@/views/TextCompletions'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import FileCompletions from '@/views/FileCompletions'
import FileMsgClass from '@/views/FileMsgClass'
import { generateChatId } from '@/utils/deepseek'
import { MSG_TYPE } from '../utils/constant'
import { MessageBox } from 'element-ui'
import { getToken, getAccess_token } from '@/utils/auth'
import { getHistories, searchfuncInfo } from '@/api/deepSeekApi'
import { set } from 'nprogress'
import { eventBus } from '@/utils/eventBus'
/**  */
const appId =
  process.env.NODE_ENV === 'production' ? '67a86833ee92f8ff6e2ccebe' : '67c811aa870793870db6010a'
const baseUrl = process.env.VUE_APP_BASE_API
export default {
  data() {
    return {
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile',
      baseUrl: baseUrl,
      textList: [], // 提示词列表，应该是数组类型
      textarea: '',
      funTitle: '',
      msgType: 'common',
      ctrl: null, // 终止fetchEventSource请求的变量
      showHelpRead: false, // 是否显示语音助读终止按钮
      isThinking: false, // 是否正在思考
      startReading: false, // 开始语音助读
      sseStreamEnded: false, // SSE流是否已结束
      fastRenderMode: false, // 快速渲染模式，用于处理快速SSE流
      lastChunkTime: 0, // 上次接收数据的时间
      chunkCount: 0, // 短时间内接收的数据块数量
      totalCharsInWindow: 0, // 时间窗口内接收的总字符数
      renderingScrollTimer: null // 渲染过程滚动监控定时器
    }
  },
  methods: {
    /**
     * 实时获取当前请求应该使用的参数
     * 这个方法会根据当前的状态实时计算正确的参数
     */
    getCurrentRequestParams() {
      // 当使用特定功能时，强制使用"不使用知识库"
      const functionsRequireNoKnowledge = ['文档校对', '文档总结', '中英互译', 'excel助手', '提示词帮助']

      let knowledgeCode = this.knowledgeCode
      let bizType = this.msgType

      if (this.funTitle && functionsRequireNoKnowledge.includes(this.funTitle)) {
        knowledgeCode = 'SPIC-NON'
        console.log('功能', this.funTitle, '强制使用不使用知识库:', knowledgeCode)
      } else {
        console.log('普通对话使用用户选择的知识库:', knowledgeCode)
      }

      // 如果使用了提示词，确保bizType为doctips
      console.log('提示词检查:', {
        textarea: this.textarea,
        textList: this.textList,
        textListType: typeof this.textList,
        textListLength: this.textList.length,
        condition: this.textarea !== '' && this.textList.length > 0
      })

      if (this.textarea !== '' && this.textList.length > 0) {
        bizType = 'doctips'
        console.log('检测到提示词使用，bizType设置为:', bizType)
      }

      return {
        knowledgeCode,
        bizType
      }
    },
    /** 终止sse请求 */
    stopFetchSource() {
      this.isThinking = false
      if (this.ctrl) {
        console.log('主动终止sse请求')
        // this.ctrl.stop()
        this.ctrl.abort()
        this.ctrl = null
      }
    },
    /** 发送消息按钮 */
    async sendClick(type) {
      console.log('sendClick', this.funTitle, this.msgType)
      // 每次发送新内容时 重置滚动flag状态 并且滚动到最底部
      this.autoScroll = true
      this.userScrolling = false // 重置用户滚动状态
      this.isRenderCompleted = false // 重置渲染完成状态
      this.totalContentLength = 0 // 重置总内容长度
      this.renderedContentLength = 0 // 重置已渲染内容长度
      this.scrolledTimes = 0 // 重置滚动次数
      this.scrollToBottom('usesse 45')

      this.showTranslateBar = false
      this.isShowFunBox = false
      this.excelbox = false
      if (type === 'stop') {
        this.sendClickContent(type)
      } else if (this.online) {
        searchfuncInfo().then((res) => {
          if (res.code === 200) {
            if (res.data.status === 1) {
              MessageBox.alert(
                `尊敬的用户，今日联网搜索次数已达上限。搜索次数将于每日零点自动重置，届时欢迎您继续使用。`,
                `联网次数用尽`,
                {
                  dangerouslyUseHTMLString: true,
                  showConfirmButton: false,
                  customClass: this.disabledTrigger ? 'mobile-message-box' : '',
                  callback: () => {
                    this.online = 0
                    this.sendClickContent(type)
                  }
                }
              )
            } else {
              this.sendClickContent(type)
            }
          }
        })
      } else {
        this.sendClickContent(type)
      }
    },
    async sendClickContent(type) {
      // console.log(type, this.textarea, this.fileList.length, '什么情况')
      if (!type && !this.textarea.trim() && this.fileList.length === 0) {
        return
      }
      // 如果正在思考中，则主动停止
      if (this.isThinking) {
        // this.$message.warning("正在聊天中...请等待结束");
        // 停止思考的时候主动重置标题参数
        // 检查是否为持续性功能（excel助手等）
        const isExcelMode = this.isShowExcelBox || this.ExcelModel
        const isPersistentFunction = isExcelMode
        if (!isPersistentFunction) {
          this.funTitle = ''
          this.msgType = 'common'
        }

        console.log('停止思考，状态检查:', {
          isExcelMode,
          isPersistentFunction,
          funTitle: this.funTitle,
          msgType: this.msgType
        })
        this.stopFetchSource()
        this.isThinking = false
        const length = this.msgList.length
        this.msgList = this.msgList.map((item, i) => {
          if (i === length - 1) {
            item.status = ''
          }

          return item
        })
        // console.log(this.msgList, '什么意思Stop')
        return
      }
      console.log('sendClickContent', this.funTitle)

      // 确保msgType设置逻辑与上传时一致
      if (this.funTitle && MSG_TYPE[this.funTitle]) {
        this.msgType = MSG_TYPE[this.funTitle]
      } else if (this.fileList && this.fileList.length) {
        this.msgType = 'common'
      }
      console.log('sendClickContent', this.msgType)
      // 如果使用了提示词
      if (this.textarea !== '' && this.textList.length > 0) {
        this.msgType = 'doctips'
      }
      // console.log('msgType:', this.msgType)
      // console.log('shortcut check')
      if (params) {
        params.bizType = this.msgType
        console.log('设置bizType:', params.bizType)
      }
      if (
        ['中英互译', '文档校对', '文档总结', '文档阅读', '提示词帮助', 'excel助手'].includes(
          this.textarea.trim()
        ) &&
        this.fileList.length
      ) {
        this.usedShortcutInCurSessionNumber = this.usedShortcutInCurSessionNumber + 1
      }
      if (this.usedShortcutInCurSessionNumber > 1 || this.showFirst) {
        console.log(
          1,
          'usedShortcutInCurSessionNumber',
          this.usedShortcutInCurSessionNumber,
          'showFirst',
          this.showFirst,
          'disabledTrigger',
          this.disabledTrigger
        )
        if (this.disabledTrigger) {
          console.log(
            2,
            'usedShortcutInCurSessionNumber',
            this.usedShortcutInCurSessionNumber,
            'showFirst',
            this.showFirst,
            'disabledTrigger',
            this.disabledTrigger
          )
          await this.initChat()
        } else {
          console.log(
            3,
            'usedShortcutInCurSessionNumber',
            this.usedShortcutInCurSessionNumber,
            'showFirst',
            this.showFirst,
            'disabledTrigger',
            this.disabledTrigger
          )
          await this.getChatHistoryList(1)
          await this.newChatClick('129')
        }
        await this.sendClick()
        return
      }
      const dataId = generateChatId(24)
      const chartId = this.disabledTrigger ? this.activeChatId : this.curChat.chatId
      // 创建human message放入msgList中
      // const msg = new TextMsgClass(dataId, 'Human', this.textarea)
      const msg = new FileMsgClass(dataId, 'Human', this.textarea, this.fileList)
      console.log('msg', msg)
      this.msgList.push(msg)
      // 组装上传参数
      let params = ''
      // todo
      if (this.textarea && this.fileList.length > 0) {
        // 文字+文件
        params = new FileCompletions(
          dataId,
          this.textarea,
          appId,
          chartId,
          // "shareChat-1741425008504-wybMmtwWk9k3KdEATvwOLRht",
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid'),
          this.fileList,
          null,
          this.translateFrom,
          this.translateTo
        )
        // params.messages[0].content.unshift({
        //   type: 'text',
        //   text: this.textarea
        // })
      } else if (this.textarea && this.fileList.length === 0) {
        // 纯文字
        params = new TextCompletions(
          dataId,
          this.textarea,
          appId,
          chartId,
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid')
        )
      } else if (!this.textarea && this.fileList.length > 0) {
        // 纯文件
        params = new FileCompletions(
          dataId,
          this.textarea,
          appId,
          chartId,
          // "shareChat-1741425008504-wybMmtwWk9k3KdEATvwOLRht",
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid'),
          this.fileList,
          null,
          this.translateFrom,
          this.translateTo
        )
      }
      params.bizType = this.msgType
      if (this.isElectronic) {
        params.dtyFile = 'DTY'
      } else {
        params.dtyFile = ''
      }
      // 组装上传参数END
      console.log('params', params)
      this.sseReq(params)
      setTimeout(() => {
        eventBus.$emit('refreshPoints')
      }, 200)
    },
    async sseReq(params) {
      // 表示在数据处理过程中保留原始数据集的引用信息
      params.retainDatasetCite = true

      // 实时获取当前的参数状态（重要：每次请求时都重新计算）
      const currentParams = this.getCurrentRequestParams()

      params.variables = {
        llm_type: this.llm_type,
        search_func: this.online,
        knowledge_code: currentParams.knowledgeCode
      }

      // 始终使用实时计算的bizType，确保参数正确
      params.bizType = currentParams.bizType

      console.log('SSE请求参数:', {
        funTitle: this.funTitle,
        bizType: params.bizType,
        knowledge_code: currentParams.knowledgeCode,
        msgType: this.msgType
      })
      params.staffId = localStorage.getItem('staffId')
      params.visitFrom = localStorage.getItem('visitFrom')
      // 重置滚动和渲染状态
      this.autoScroll = true
      this.userScrolling = false
      this.isRenderCompleted = false
      this.totalContentLength = 0
      this.renderedContentLength = 0
      this.scrolledTimes = 0 // 重置滚动次数
      this.sseStreamEnded = false // 重置SSE流结束标志
      this.fastRenderMode = false // 重置快速渲染模式
      this.lastChunkTime = 0 // 重置数据接收时间
      this.chunkCount = 0 // 重置数据块计数
      this.totalCharsInWindow = 0 // 重置时间窗口字符数

      // 清除渲染滚动监控定时器
      if (this.renderingScrollTimer) {
        clearInterval(this.renderingScrollTimer)
        this.renderingScrollTimer = null
      }

      this.scrollToBottom('usesee sseReq')
      this.textarea = ''
      this.textList = []
      this.fileList = []
      // 注意：不要在这里重置msgType和funTitle，因为它们在请求过程中需要保持
      // this.msgType = 'common'  // 移除：会导致功能参数错误
      // this.funTitle = ''       // 移除：会导致功能状态丢失
      this.isThinking = true
      // if (this.funTitle === '文档阅读') {
      //   this.showHelpRead = true
      // }
      /** * 获取任务列表-SSE传输模式 */
      const aiMessage = new TextMsgClass(params.responseChatItemId, 'AI', '', '', 'thinking')
      console.log(aiMessage, 'aiMessage')
      this.msgList.push(aiMessage)
      this.ctrl = new AbortController()
      const _this = this
      let token = ''
      // 非开发环境
      // console.log('process.env.NODE_ENV', process.env.NODE_ENV)
      // if (process.env.NODE_ENV !== 'development') {
      token =
        'Bearer ' +
          (localStorage.getItem('token') || getToken() || sessionStorage.getItem('token')) ||
        getToken() ||
        getAccess_token()
      // console.log('token', token)
      // }

      await fetchEventSource(_this.baseUrl + '/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params),
        signal: this.ctrl.signal,
        openWhenHidden: true, // 页面隐藏时保持连接
        onmessage(msg) {
          console.log('SSE接收信息 链接')
          _this.handleChunk(msg, aiMessage)
        },
        onclose() {
          console.log('SSE接收信息 断开')
          _this.stopFetchSource()
          // 确保在连接关闭后也能正确滚动到底部
          if (_this.disabledTrigger && _this.autoScroll) {
            // 手机端：确保所有剩余内容都被渲染并滚动到底部
            setTimeout(() => {
              // 如果还有渲染队列中的内容，先完成渲染
              if (_this.renderQueue && _this.renderQueue.length > 0 && !_this.isRendering) {
                _this.renderText()
              }

              // 标记渲染完成并滚动
              setTimeout(() => {
                if (_this.isRendering) {
                  _this.isRendering = false
                  _this.isRenderCompleted = true
                }
                _this.scrollToBottom('sse-onclose')

                // 额外的延迟滚动确保内容完全显示
                setTimeout(() => {
                  if (_this.autoScroll && !_this.userScrolling) {
                    _this.scrollToBottom('sse-onclose-final')
                  }
                }, 200)
              }, 100)
            }, 50)
          }
        },
        onerror(err) {
          console.log('SSE接收信息 异常', err)
          // 在调用流的时候可能遇到 sse流504错误 实际上这个流已经断开了
          if (aiMessage.status !== 'sensitive') {
            aiMessage.status = 'error'
          }
          _this.stopFetchSource()
          // 确保在出错后也能正确滚动到底部
          if (_this.disabledTrigger && _this.autoScroll) {
            // 手机端：即使出错也要确保内容显示完整并滚动到底部
            setTimeout(() => {
              // 如果还有渲染队列中的内容，先完成渲染
              if (_this.renderQueue && _this.renderQueue.length > 0 && !_this.isRendering) {
                _this.renderText()
              }

              // 标记渲染完成并滚动
              setTimeout(() => {
                if (_this.isRendering) {
                  _this.isRendering = false
                  _this.isRenderCompleted = true
                }
                _this.scrollToBottom('sse-onerror')

                // 额外的延迟滚动确保内容完全显示
                setTimeout(() => {
                  if (_this.autoScroll && !_this.userScrolling) {
                    _this.scrollToBottom('sse-onerror-final')
                  }
                }, 200)
              }, 100)
            }, 50)
          }
          throw err
        }
      })
    },
    // 播放语音
    async playAudio() {
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel() // 停止当前播放
      }
      if (this.speechList.length && !this.startReading) {
        this.startReading = true // 标记正在播放
        const text = this.productSegments('').replace(/[#]/g, '') // 从队列中取出一段文字
        const msg = new SpeechSynthesisUtterance(text)
        const voices = window?.speechSynthesis?.getVoices?.() || [] // 获取语言包
        const voice = voices.find((item) => item.lang === 'zh-CN')
        msg.rate = 1
        if (voice) {
          msg.voice = voice
        }
        msg.onend = () => {
          this.startReading = false // 标记播放结束
          this.playAudio() // 递归调用播放下一段
        }
        msg.onerror = () => {
          console.error('语音播放出错')
          this.startReading = false // 标记播放结束
          if (this.showHelpRead) {
            this.playAudio() // 继续播放下一段
          }
        }
        window.speechSynthesis.speak(msg)
      }
    },
    handleChunk(chunk, aiMessage) {
      // 处理每一部分数据，例如更新消息列表
      const { event, data } = chunk
      // 获取当前时间精确到毫秒转换当前时间
      const formattedTime = new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
      })
      console.log(`SSE ${formattedTime} ${event}接收到数据:`, data)
      // console.log('chunk', event, data)
      const parsedData = data !== '[DONE]' ? JSON.parse(data) : null
      // 联网次数用尽
      // if (event === 'searchFunc') {
      //   this.stopFetchSource()
      //   MessageBox.alert(`尊敬的用户，今日联网搜索次数已达上限。搜索次数将于每日零点自动重置，届时欢迎您继续使用。`, `联网次数用尽`, {
      //     dangerouslyUseHTMLString: true,
      //     // showClose: notify.canClosed === 1,
      //     showConfirmButton: false,
      //     customClass: this.disabledTrigger ? 'alert-message-mobile-box' : '',
      //     callback: () => {
      //     }
      //   })
      //   return false
      // } else
      //   else if (event === 'fastAnswer') {
      //   const reference = parsedData?.choices[0]?.delta.content || ''
      //   this.$set(aiMessage.value[2].text, 'content', reference)
      //   // 回答过程
      // }
      // 敏感词
      if (event === 'sensitive') {
        // 即使是敏感词内容，也要显示出来
        const sensitiveContent = parsedData?.choices[0]?.delta.content || ''
        if (this.disabledTrigger) {
          // mobile 端处理
          this.totalContentLength += sensitiveContent.length
          this.renderQueue.push(...sensitiveContent)

          // 启动渲染过程（如果尚未启动）
          if (!this.isRendering && this.renderQueue.length > 0) {
            this.renderText()
          }
        } else {
          // PC 端处理
          const newContent = aiMessage.value[1].text.content + sensitiveContent
          this.$set(aiMessage.value[1].text, 'content', newContent)
        }

        // 设置为特殊状态，表示包含敏感词
        aiMessage.status = 'sensitive'
      } else if (event === 'flowNodeStatus') {
        aiMessage.status = parsedData?.status
      } else if ((event === 'answer' && parsedData) || (event === 'fastAnswer' && parsedData)) {
        // 正文
        const resText = parsedData?.choices[0]?.delta.content || ''
        if (this.disabledTrigger) { // mobile 端的标识
          // 先获取内容数据
          const reasoningText = parsedData?.choices[0]?.delta.reasoning_content || ''

          // 计算数据到达速度，判断是否为快速SSE流
          const now = Date.now()
          const currentChunkSize = reasoningText.length + resText.length

          // 重置时间窗口（每1秒重置一次）
          if (now - this.lastChunkTime > 1000) {
            this.chunkCount = 0
            this.totalCharsInWindow = 0
          }

          this.chunkCount++
          this.totalCharsInWindow += currentChunkSize
          this.lastChunkTime = now

          // 判断是否为快速SSE流：
          // 1. 1秒内接收超过3个数据块，或
          // 2. 1秒内接收超过200个字符，或
          // 3. 单次数据块超过100个字符
          const isHighFrequency = this.chunkCount > 3
          const isHighVolume = this.totalCharsInWindow > 200
          const isLargeChunk = currentChunkSize > 100

          if (isHighFrequency || isHighVolume || isLargeChunk) {
            if (!this.fastRenderMode) {
              console.log(`检测到快速SSE流: 频率=${this.chunkCount}/s, 字符=${this.totalCharsInWindow}/s, 当前块=${currentChunkSize}字符`)
              this.fastRenderMode = true
            }
          }

          // 处理思考过程内容
          if (reasoningText) {
            // 使用更高效的字符分解方式
            this.renderQueueReasoning.push(...reasoningText)
          }

          // 处理正文内容
          if (resText) {
            this.totalContentLength += resText.length
            // 使用更高效的字符分解方式
            // 这样即使AI一次性返回完整内容，也能实现边渲染边滚动
            this.renderQueue.push(...resText)
          }

          // 启动渲染过程（如果尚未启动）
          if (!this.isRendering && (this.renderQueue.length > 0 || this.renderQueueReasoning.length > 0)) {
            this.renderText()
          }
        } else {
          // PC 端保持原有逻辑
          const newContent = aiMessage.value[1].text.content + resText
          // 思考过程
          const reasoning =
            aiMessage.value[0].reasoning.content +
            (parsedData?.choices[0]?.delta.reasoning_content || '')
          this.$set(aiMessage.value[0].reasoning, 'content', reasoning)
          this.$set(aiMessage.value[1].text, 'content', newContent)
        }
        //         console.log("aiMessage", aiMessage, "内容", resText);
        if (this.funTitle === '文档阅读' && resText && !this.disabledTrigger) {
          this.speechList.push(resText)
          if (!this.startReading && this.showHelpRead) {
            console.log('开始播放', this.speechList)
            this.startReading = true
            setTimeout(() => {
              this.startReading = false
              this.playAudio()
            }, 1500)
          }
          //           window?.speechSynthesis?.cancel();
        }
        if (parsedData?.choices[0]?.finish_reason === 'stop') {
          this.isThinking = false
          aiMessage.status = ''
          // 判断是否需要清空funTitle - 只有在非持续性功能时才清空
          // excel助手(PC端: isShowExcelBox, 手机端: ExcelModel)是持续性功能，不应该清空
          const isExcelMode = this.isShowExcelBox || this.ExcelModel
          const isPersistentFunction = isExcelMode

          if (!isPersistentFunction) {
            // 只有在非持续性功能完成后才重置状态
            this.funTitle = ''
            this.msgType = 'common'
          }

          console.log('SSE流结束，状态检查:', {
            isExcelMode,
            isPersistentFunction,
            funTitle: this.funTitle,
            msgType: this.msgType
          })

          // 标记SSE流已结束
          this.sseStreamEnded = true

          // 如果是 mobile 端且渲染队列还有内容，确保全部渲染完
          if (this.disabledTrigger && (this.renderQueue.length > 0 || this.renderQueueReasoning.length > 0)) {
            // 触发一次最终渲染确保所有内容都显示
            if (!this.isRendering) {
              this.renderText()
            }
            console.log('SSE结束，但还有渲染队列，继续渲染')
          } else if (this.disabledTrigger) {
            // 没有渲染队列，但可能还有异步内容（如点赞按钮）需要渲染
            // 延迟滚动以确保所有内容都已渲染
            setTimeout(() => {
              if (this.autoScroll && !this.userScrolling) {
                this.scrollToBottom('finish-stop-no-queue')
              }
            }, 200) // 增加延迟时间
          }

          if (this.disabledTrigger) {
            this.getChatMsgList()
            this.getHistoryList()
          } else {
            // PC端也要确保最终滚动
            setTimeout(() => {
              if (this.autoScroll && !this.userScrolling) {
                this.scrollToBottom('finish-stop-pc')
              }
            }, 100)

            getHistories({
              offset: 0,
              pageSize: this.pageSize,
              appId: appId,
              source: 'online'
            }).then((res) => {
              this.historyList = [...res.data.list]
              const currentChat = this.historyList.find(
                (item) => item.chatId === this.curChat.chatId
              )
              this.curChat = currentChat || this.curChat
              this.historyItemClick(this.curChat)
              // this.curChat = this.historyList[0]
            })
          }
        }
      } else if (event === 'flowResponses') {
        aiMessage.status = ''
      } else {
        // console.log('SSE流 - 其他事件类型:', event, '数据:', data)
      }

      // 优化滚动逻辑：区分手机端和PC端的处理方式
      if (this.autoScroll) {
        if (this.disabledTrigger) {
          // 手机端：不使用防抖，立即滚动，让渲染过程中的滚动更及时
          // 手机端的滚动主要由renderText()方法中的逻辑处理
          // 这里只处理非渲染状态下的滚动需求
          if (!this.isRendering) {
            this.scrollToBottom('usesse-mobile-immediate')
          }
        } else {
          // PC端：保持原有的防抖机制
          if (this.scrollTimer) {
            clearTimeout(this.scrollTimer)
          }
          this.scrollTimer = setTimeout(() => {
            this.scrollToBottom('usesse-pc-debounced')
          }, 50) // 50ms防抖
        }
      }
    }
  }
}
