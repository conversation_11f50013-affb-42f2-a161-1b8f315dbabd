class TextMsgClass {
  constructor(dataId, obj, content, reasoning, status = '') {
    this.dataId = dataId
    this.hideInUI = false
    this.obj = obj
    this.status = status
    this.value = [
      {
        type: 'reasoning',
        reasoning: {
          content: content
        }
      },
      {
        type: 'text',
        text: {
          content: content
        }
      },
      {
        type: 'text',
        text: {
          content: content
        }
      }
    ]
    this.time = new Date().toISOString() // ISO 8601格式
  }

  // 获取完整消息对象
  getMessage() {
    return {
      dataId: this.dataId,
      hideInUI: this.hideInUI,
      obj: this.obj,
      value: this.value,
      time: this.time
    }
  }

  // 更新消息内容
  updateContent(newContent) {
    this.value[0].reasoning.content = newContent
    this.time = new Date().toISOString()
  }

  // 获取当前内容
  getContent() {
    return this.value[0].reasoning.content
  }

  // 获取格式化时间
  getFormattedTime() {
    return new Date(this.time).toLocaleString()
  }
}

export default TextMsgClass
