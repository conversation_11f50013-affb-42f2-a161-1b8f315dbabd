<template>
  <div class="mobile-container">
    <div class="service-hotline">
      <div class="chat_history_button">
        <svg-icon icon-class="expand" class="icon-top" @click="openHistoryList" />
        <span style="margin-left: 8px" @click="handleTopCommand('newChat')">
          <svg-icon icon-class="chat1" class="icon-top" />
        </span>
      </div>
      <div class="flex">
        <div class="tip_text">服务热线：010-66298888转3</div>
      </div>
    </div>
    <!-- 未开始聊天的时候显示海尔欢迎页面 -->
    <div v-if="msgList.length === 0" class="logo-introduction">
      <div class="logo">
        <svg-icon icon-class="deepSeek" class="sp-deepSeek" />
      </div>
      <div class="introduction">
        <div style="font-size: 24px; font-weight: bolder; padding: 15px 0">
          Hi！我是 SPIC DeepSeek
        </div>
        <div style="padding: 0 40px; font-size: 14px; color: #8c979f; line-height: 20px">
          我可以帮你搜索、答疑、写作！请注意：请勿上传任何涉密文件或敏感信息哦~
        </div>
        <div class="ltitle">
          <!-- <svg-icon icon-class="heart" style="font-size: 18px; margin-right: 6px"></svg-icon> -->
          <span>
            已解答
            <!-- <span class="font">{{ interactionTotal }}</span>问题</span> -->
            <span v-if="loading" class="font loading-placeholder">--</span>
            <span v-else class="font">{{ interactionTotal }}</span>
            个问题
          </span>
        </div>
      </div>
    </div>
    <!-- 开始聊天的时候显示的对话页面 -->
    <div v-else ref="chatArea" class="chat-area" @scroll="handleScroll">
      <div v-for="msg in msgList" :key="msg.dataId">
        <com-msg-left
          v-if="msg.obj.toLowerCase() === 'ai'"
          ref="comMsgLeftRef"
          :info="msg"
          :app-id="appId"
          :chat-id="activeChatId"
        />
        <com-msg-right
          v-if="msg.obj.toLowerCase() === 'human'"
          :info="msg"
          :app-id="appId"
          :chat-id="activeChatId"
          @sendComMsgRightReSubmit="sendComMsgRightReSubmit"
          @sendComMsgRightDel="sendComMsgRightDel"
          @sendComMsgRightEdit="sendComMsgRightEdit"
        />
      </div>
      <!-- 回到最新位置按钮 -->
      <div v-if="showBackToBottom" class="back-to-bottom-container">
        <div class="back-to-bottom-btn" @click="forceScrollToBottom">
          <i class="el-icon-arrow-down" style="font-size: 20px" />
        </div>
      </div>
    </div>
    <div
      v-if="textarea.includes(['中英互译']) && showTranslateBar && false"
      class="translate-bar flex-text"
      style="margin-right: 12px"
    >
      <el-select
        v-model="translateFrom"
        size="mini"
        style="width: 100px"
        @change="handleDocument('中英互译')"
      >
        <el-option label="自动检测" value="自动检测" />
        <el-option label="中文" value="中文" />
        <el-option label="英文" value="英文" />
        <!-- 可扩展更多语言 -->
      </el-select>
      <span style="margin: 0 4px; color: #a3a3a3">⇄</span>
      <el-select
        v-model="translateTo"
        size="mini"
        style="width: 70px"
        @change="handleDocument('中英互译')"
      >
        <el-option label="中文" value="中文" />
        <el-option label="英文" value="英文" />
        <!-- 可扩展更多语言 -->
      </el-select>
    </div>

    <!-- 快捷功能区域 -->
    <div v-if="fileList.length === 0 && !ExcelModel" class="shortcut-area">
      <div class="container">
        <!-- 文档校对 -->
        <el-popover
          ref="filePopover1"
          placement="top"
          width="120"
          trigger="click"
          style="padding: 0"
        >
          <ul class="document-summary-list">
            <li @click="selectChatFile(1, '文档校对')">
              <svg-icon icon-class="spic-wx-logo" />
              电投壹文件
            </li>
            <li @click="handleExternalClick(1, '文档校对', false)">
              <svg-icon icon-class="local-files" />
              本地文件
            </li>
          </ul>
          <div
            slot="reference"
            style="
              padding: 5px 10px;
              margin-left: 10px;
              background-color: #fff;
              margin: 4px;
              box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
              display: inline-block;
              border-radius: 5px;
              line-height: 22px;
            "
            @click="handleDocument('文档校对')"
          >
            <span style="color: #090909">文档校对</span>
            <span style="color: #494949; margin-left: 4px">
              <svg-icon icon-class="documentTest1" />
            </span>
          </div>
        </el-popover>
        <!-- 文档总结 -->
        <el-popover
          ref="filePopover2"
          placement="top"
          width="100"
          trigger="click"
          style="padding: 0"
        >
          <ul class="document-summary-list">
            <li @click="selectChatFile(2, '文档总结')">
              <svg-icon icon-class="spic-wx-logo" />
              电投壹文件
            </li>
            <li @click="handleExternalClick(2, '文档总结', false)">
              <svg-icon icon-class="local-files" />
              本地文件
            </li>
          </ul>
          <div
            slot="reference"
            style="
              padding: 5px 10px;
              margin-left: 10px;
              background-color: #fff;
              margin: 4px;
              box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
              display: inline-block;
              border-radius: 5px;
              line-height: 22px;
            "
            @click="handleDocument('文档总结')"
          >
            <span style="color: #090909">文档总结</span>
            <span style="color: #494949; margin-left: 4px">
              <svg-icon icon-class="documentSum1" />
            </span>
          </div>
        </el-popover>
        <!-- 文档阅读 (不显示)-->
        <el-popover
          v-if="false"
          ref="filePopover3"
          placement="top"
          width="100"
          trigger="click"
          style="padding: 0"
        >
          <ul class="document-summary-list">
            <li @click="selectChatFile(3, '文档阅读')">
              <svg-icon icon-class="spic-wx-logo" />
              电投壹文件
            </li>
            <li @click="handleExternalClick(3, '文档阅读', false)">
              <svg-icon icon-class="local-files" />
              本地文件
            </li>
          </ul>
          <div
            slot="reference"
            style="
              padding: 5px 10px;
              margin-left: 10px;
              background-color: #fff;
              margin: 4px;
              box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
              display: inline-block;
              border-radius: 5px;
              line-height: 22px;
            "
            @click="handleDocument('文档阅读')"
          >
            <span style="color: #090909">文档阅读</span>
            <span style="color: #494949; margin-left: 4px">
              <svg-icon icon-class="docReadAssist" />
            </span>
          </div>
        </el-popover>
        <!-- 中英互译 -->
        <el-popover
          ref="filePopover6"
          placement="top"
          width="120"
          trigger="click"
          style="padding: 0"
        >
          <ul class="document-summary-list">
            <li @click="selectChatFile(6, '中英互译', true)">
              <svg-icon icon-class="spic-wx-logo" />
              电投壹文件
            </li>
            <li @click="handleExternalClick(6, '中英互译', true)">
              <svg-icon icon-class="local-files" />
              本地文件
            </li>
          </ul>
          <div
            slot="reference"
            style="
              padding: 5px 10px;
              margin-left: 10px;
              background-color: #fff;
              margin: 4px;
              box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
              display: inline-block;
              border-radius: 5px;
              line-height: 22px;
            "
            @click="handleDocument('中英互译')"
          >
            <span style="color: #090909">中英互译</span>
            <span style="color: #494949; margin-left: 4px;">
              <svg-icon icon-class="translate1" />
            </span>
          </div>
        </el-popover>
        <!-- excel助手 -->
        <div class="content-help" @click="excelHelpClick">
          <span style="color: #090909">excel助手</span>
          <span style="color: #494949; margin-left: 4px">
            <svg-icon icon-class="excelAssistant1" />
          </span>
        </div>
        <!-- 提示词帮助 -->
        <div class="content-help" @click="assistantClick">
          <span style="color: #090909">提示词帮助</span>
          <span style="color: #494949; margin-left: 4px">
            <svg-icon icon-class="content-help1" />
          </span>
        </div>
      </div>
    </div>

    <div :class="[ExcelModel ? 'edit-box' : 'edit-box-2', isIOSDevice ? 'margin-bottom-ios' : '']">
      <!-- 文件列表显示区域-->
      <div v-if="fileList.length > 0 && !ExcelModel" class="file-preview-area">
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <file-preview :file="item" :img-type="['jpg', 'jpeg', 'png', 'bmp']" />
          <div class="delete-file">
            <i class="el-icon-error" @click="deleteFile(index)" />
          </div>
        </div>
        <!--
        上传文件后面的加号
        selectChatFile，handleExternalClick这两个方法不传参数
        funTitle和ifImageTypes这两个参数会取前面文档校对之类的按钮参数
        -->
        <el-popover ref="filePopover4" placement="top" trigger="click">
          <ul class="document-summary-list">
            <li @click="selectChatFile(4)">
              <svg-icon icon-class="spic-wx-logo" />
              电投壹文件
            </li>
            <li @click="handleExternalClick(4)">
              <svg-icon icon-class="local-files" />
              本地文件
            </li>
          </ul>
          <div
            slot="reference"
            class="add-file-icon"
          >
            <svg-icon icon-class="add-files" />
          </div>
        </el-popover>
      </div>
      <!-- excel 快捷 -->
      <div v-if="ExcelModel" class="excelHandel">
        <div class="title">
          <svg-icon icon-class="excelAssistant" />
          excel助手
        </div>
        <!-- 文件列表显示区域-->
        <div v-if="fileList.length > 0 && ExcelModel" class="file-preview-area">
          <div v-for="(item, index) in fileList" :key="index" class="file-item">
            <file-preview :file="item" :img-type="['jpg', 'jpeg', 'png', 'bmp']" />
            <div class="delete-file">
              <i class="el-icon-error" @click="deleteFile(index)" />
            </div>
          </div>
          <!--
        上传文件后面的加号
        selectChatFile，handleExternalClick这两个方法不传参数
        funTitle和ifImageTypes这两个参数会取前面文档校对之类的按钮参数
        -->
          <el-popover ref="filePopover4" placement="top" trigger="click">
            <ul class="document-summary-list">
              <li @click="selectChatFile(4)">
                <svg-icon icon-class="spic-wx-logo" />
                电投壹文件
              </li>
              <li @click="handleExternalClick(4)">
                <svg-icon icon-class="local-files" />
                本地文件
              </li>
            </ul>
            <div
              slot="reference"
              class="add-file-icon"
            >
              <svg-icon icon-class="add-files" />
            </div>
          </el-popover>
        </div>
        <Excellist :excel-box="excelBox" @handleExcelClick="handleExcelClick" />
        <div class="close-excel-handel">
          <i class="el-icon-error" style="font-size: 22px" @click="closeExcel" />
        </div>
      </div>
      <!-- 底部编辑发送区域 -->
      <div v-show="ExcelModel" class="line" />
      <div class="sent-area">
        <div class="input-line">
          <!-- 话筒 -->
          <mobile-audio
            ref="mobileAudio"
            v-model="textarea"
            :list="textList"
            :show-excel-helper="ExcelModel"
            :excel-box="excelBox"
            @transAudioSuccess="transAudioSuccess"
            @send="sendClick()"
            @exitExcel="closeExcel"
            @textareaChange="handleTextareaChange"
          />
          <div v-show="ExcelModel">
            <div
              v-if="ExcelModel && (fileList.length > 0 || textarea.length > 0) && !isThinking"
              class="sent-btn"
            >
              <svg-icon icon-class="send-MessageAct" @click="sendClick()" />
            </div>
            <div v-else-if="ExcelModel && fileList.length == 0 && isThinking">
              <svg-icon icon-class="thinking" class="think-btn" @click="sendClick('stop')" />
            </div>
            <el-popover
              v-else
              ref="filePopover7"
              placement="top"
              width="100"
              trigger="click"
              style="padding: 0"
            >
              <ul class="document-summary-list">
                <li @click="selectChatFile(7)">
                  <svg-icon icon-class="spic-wx-logo" />
                  电投壹文件
                </li>
                <li @click="handleExternalClick(7)">
                  <span
                    style="color: #606266 !important; display: inline-flex; align-items: center"
                    class="no-color-change"
                  >
                    <svg-icon icon-class="local-files" style="margin-right: 4px" />
                    本地文件
                  </span>
                </li>
              </ul>

              <span
                slot="reference"
                style="font-size: 20px; color: #090909"
                class="mobile-audio-icon"
              >
                <svg-icon icon-class="add-files-2" />
              </span>
            </el-popover>
          </div>
        </div>
        <div v-show="!ExcelModel" class="msg-config-line">
          <!-- 模型切换按钮 -->
          <div class="model-switch">
            <chat-model
              v-model="llm_type"
              :fun-title="funTitle"
              :online.sync="online"
              :knowledge-type.sync="knowledgeCode"
              bg="#EBF5E8"
            />
          </div>
          <div class="upload-file" />
          <el-popover
            ref="filePopover5"
            placement="top"
            width="100"
            trigger="click"
            style="padding: 0"
          >
            <ul class="document-summary-list">
              <li @click="selectChatFile(5)">
                <svg-icon icon-class="spic-wx-logo" />
                电投壹文件
              </li>
              <li @click="triggerUpload(5)">
                <el-upload
                  ref="uploader"
                  class="upload-demo"
                  :action="baseUrl + '/api/common/file/upload'"
                  :on-change="handleFileChange"
                  :on-error="handleUploadError"
                  :on-exceed="handleExceed"
                  :on-progress="onProgress"
                  :before-upload="beforeAvatarUpload"
                  multiple
                  auto-upload
                  :headers="uploadHeaders"
                  :data="{
                    metadata: JSON.stringify({ chatId: activeChatId }),
                    bucketName: 'chat',
                    data: JSON.stringify({
                      appId: appId,
                      outLinkUid: outLinkUid,
                      ...(shareId ? { shareId } : {})
                    })
                  }"
                  :limit="8"
                  :show-file-list="false"
                  :on-success="handleUploadSuccess"
                  :file-list="fileList"
                >
                  <!-- 使用内联样式直接覆盖所有状态的颜色 -->
                  <!-- 清空逻辑@click="changeImageTypes" -->
                  <span
                    style="color: #606266 !important; display: inline-flex; align-items: center"
                    class="no-color-change"
                  >
                    <svg-icon icon-class="local-files" style="margin-right: 4px" />
                    本地文件
                  </span>
                </el-upload>
              </li>
            </ul>
            <span slot="reference" style="font-size: 24px">
              <svg-icon icon-class="upload4" />
            </span>
          </el-popover>
          <div class="sent-btn">
            <transition name="fade">
              <svg-icon
                v-if="isThinking"
                icon-class="thinking"
                class="think-btn"
                @click="sendClick('stop')"
              />
              <svg-icon
                v-else-if="textarea.length > 0 || fileList.length > 0"
                icon-class="send-MessageAct"
                @click="sendClick()"
              />
              <svg-icon v-else icon-class="sendMsg-new" />
            </transition>
          </div>
        </div>
      </div>
    </div>
    <!-- 侧边对话记录  -->
    <el-drawer
      :visible.sync="drawer"
      :direction="direction"
      :before-close="handleClose"
      size="80%"
      :show-close="false"
      class="chat-drawer-container"
    >
      <template #title>
        <div class="chat-title">DeepSeek内网体验版</div>
        <div
          style="display: flex; align-items: center; justify-content: space-between; height: 30px"
        >
          <div class="new-chat" @click="initChat">
            <svg-icon icon-class="chat1" />
            &nbsp;开启新对话
          </div>
          <span />
        </div>
      </template>
      <div class="chat-list-container">
        <div v-for="(el, i) of historyTypeList" :key="i">
          <div v-if="el.children && el.children.length" class="chat-title1">
            {{ el.title }}
          </div>
          <div v-if="el.children && el.children.length" class="chat-list">
            <div
              v-for="(item, index) in el.children"
              :key="index"
              ref="chatItemRef"
              class="chat-item"
              :class="{ active: activeChatId === item.chatId }"
              @click="handleChatItemClick(index, item)"
            >
              <div class="chat-title">
                {{ item.customTitle || item.title }}
              </div>
              <div class="operator flex flex-center">
                <el-dropdown trigger="click" @command="(command) => handleCommand(command, item)">
                  <span
                    class="icon el-icon-more"
                    @click.stop="
                      () => {
                        return
                      }
                    "
                  />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="toTop">
                      <svg-icon icon-class="to-top" />
                      {{ item.timeCategory === 'top' ? '取消置顶' : '置顶' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="editTitle">
                      <svg-icon icon-class="custom-title" />
                      自定义标题
                    </el-dropdown-item>
                    <el-dropdown-item command="delete">
                      <svg-icon icon-class="delete" />
                      <span>&nbsp;删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="line" />
      <div class="chatBottom">
        <!--意见对话  -->
        <div class="feedbackBtn" @click="feedbackClick">
          <svg-icon icon-class="feedback" />
          <span>意见反馈</span>
        </div>
        <!-- 用户信息 -->
        <div class="userInfo" @click="userInfoClick">
          <div class="userPic flex">
            <img src="../assets/images/userAvatar.png" alt="">
            <div class="info">
              <div class="userName">用户名：{{ this.userName || '-' }}</div>
              <!-- 总积分 -->
              <div class="total">
                <div class="total-title">
                  总积分：{{ (this.userInfo && this.userInfo.points) || 0 }}
                </div>
              </div>
            </div>
          </div>

          <div class="right">
            <svg-icon icon-class="arrow-right" />
          </div>
        </div>
      </div>
    </el-drawer>
    <!--    自定义历史记录标题对话框-->
    <el-dialog :visible.sync="dialogVisible" width="300px">
      <template #title>
        <div style="font-size: 16px">
          <i class="el-icon-edit-outline" style="color: #3874ff; font-weight: bolder" />
          &nbsp;&nbsp;自定义历史记录标题
        </div>
      </template>
      <el-input v-model="newChatTitle" />
      <template #footer>
        <span class="dialog-footer">
          <el-button size="mini" @click="dialogVisible = false">取 消</el-button>
          <el-button size="mini" type="primary" @click="modifyChatTitle()">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 提示词组件 -->
    <com-assistance ref="comAssistance" width="80%" @sendComAssistance="sendComAssistance" @comAssistanceClose="comAssistanceClose" />
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import comAudio from '@/components/deepSeek/comAudio.vue'
// import ComMsgLeft from '@/components/deepSeek/com-msg-left.vue'
import ComMsgLeft from '@/components/deepSeek/com-msg-left_renderChar.vue'
import ComMsgRight from '@/components/deepSeek/com-msg-right.vue'
import mobileAudio from '../components/deepSeek/mobile-audio.vue'
import ChatModel from '@/components/deepSeek/chat-model.vue'
import Excellist from '@/views/mobileView/cpn/excellist.vue'
import {
  getSignature,
  getAgentTicket,
  getHistories,
  chatInit1,
  chatInit,
  delHistory,
  updateHistory,
  getInitData,
  getPaginationRecords,
  transcriptions,
  itemDelete,
  uploadFile,
  getUser,
  getAddEncryptUserInfo
} from '@/api/deepSeekApi.js'
import { getInteractionTotalRequest } from '@/api/mobile.js'
import { getSysuser, userbehavior } from '@/api/points'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { FILE_TYPE, MSG_TYPE } from '../utils/constant.js'
import { getstaffId } from '@/views/mobileView/utlis.js'

const appId =
  process.env.NODE_ENV === 'production' ? '67a86833ee92f8ff6e2ccebe' : '67c811aa870793870db6010a'
import TextCompletions from './TextCompletions.js'
import TextMsgClass from './TextMsgClass.js'
import FileCompletions from './FileCompletions.js'
import FileMsgClass from '@/views/FileMsgClass'
const baseUrl = process.env.VUE_APP_BASE_API
import { generateChatId, checkOutLinkUid, getMimeType, base64ToFile } from '@/utils/deepseek.js'
import ComAssistance from '@/components/deepSeek/comAssistance.vue'
import useSse from './useSse.js'
import { eventBus } from '@/utils/eventBus'
// import { alertTest } from '@/utils/alert'

export default {
  name: 'Mobile',
  components: {
    ComAssistance,
    comAudio,
    ComMsgLeft,
    ComMsgRight,
    mobileAudio,
    ChatModel,
    Excellist
  },
  filters: {
    formatUpdateTime(value) {
      if (!value) return ''
      const now = dayjs()
      const target = dayjs(value)

      if (target.isSame(now, 'day')) {
        return target.format('HH:mm')
      }
      if (target.isSame(now.subtract(1, 'day'), 'day')) {
        return '昨天'
      }
      return target.fromNow()
    }
  },
  mixins: [useSse],
  data() {
    return {
      totalContentLength: 0, // 总内容长度
      renderedContentLength: 0, // 已渲染内容长度
      scrolledTimes: 0, // 已滚动次数，用于严格按字符数滚动
      isRenderCompleted: false, // 渲染是否完成
      lastScrollTime: 0, // 上次滚动时间，用于控制滚动频率
      renderCompletionTimer: null, // 渲染完成检查定时器
      scrollTimer: null, // 添加滚动防抖定时器
      // 添加上传headers
      uploadHeaders: {
        'X-Requested-With': 'XMLHttpRequest',
        Authorization: 'Bearer ' + localStorage.getItem('token')
      },
      interactionTotal: 0, // 互动次数查询
      loading: true,
      ifChatTitleTop: false,
      online: 0, // 是否联网搜索
      activeChatListIndex: 0,
      isChatInfoNull: true, // 判断聊天记录是否为空
      recording: false,
      fileList: [],
      drawer: false, // 侧边对话记录
      direction: 'ltr',
      dialogVisible: false, // 自定义历史记录标题对话框
      newChatTitle: '',
      imageFileExtensions: [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'bmp',
        'svg',
        'webp',
        'tiff',
        'tif',
        'psd',
        'raw',
        'heic',
        'heif',
        'avif',
        'ico',
        'cur',
        'apng',
        'pjpeg',
        'pjp'
      ],
      msgList: [],
      appId: appId,
      shareId: null,
      outLinkUid: null,
      chatHistoryList: [],
      historyTypeList: [],
      textarea: '',
      textList: [], // 提示词列表，应该是数组类型
      activeChatId: '',
      eventSourceOpen: false,
      eventSourceCtrl: null,
      partialContent: '',
      partialContentReasoning: '',
      isRendering: false,
      renderQueue: [],
      renderQueueReasoning: [],
      baseUrl: baseUrl,
      autoScroll: true,
      isIOSDevice: false,
      isThinking: false,
      currentSpeed: '1.0',
      isReading: false,
      llm_type: 1,
      usedShortcutInCurSessionNumber: 0,
      currentChatId: '',
      msgType: 'common',
      funTitle: '', // 功能标题 手机端为上传功能的判断参数
      ifImageTypes: false, // 判断是否可以传图片
      staffId: getstaffId(),
      userInfo: {
        points: 0
      }, // 存放用户信息
      userName: '',
      isElectronic: false, // 是否电投宜文件
      showTranslateBar: false, // 是否显示翻译
      translateFrom: '自动检测', // 翻译源语言
      translateTo: '英文', // 翻译目标语言
      ExcelModel: false, // excel模式的状态
      excelBox: false, // excel 模板的状态
      isAtBottom: true,
      fileUploadDebounceTimer: null,
      fileUploadDebounceDelay: 300,
      knowledgeCode: 'SPIC-NON', // 知识库类型，默认不使用知识库
      // 添加滚动控制相关变量
      userScrolling: false, // 用户是否正在手动滚动
      jsScrolling: false, // JS是否正在自动滚动
      showBackToBottom: false // 是否显示回到底部按钮
    }
  },
  watch: {
    activeChatId: {
      handler() {
        localStorage.setItem('activeChatId', this.activeChatId)
      }
    },
    chatHistoryList: {
      handler(newVal, old) {
        this.pickerHistoryType()
        // console.log(newVal, 'historyList', old)
      },
      deep: true
    },
    // 移除原来的textarea watch，改用子组件事件监听
    fileList(n, o) {
      if (!this.fileList) {
        if (this.textarea.length === 0) {
          this.funTitle = ''
        }
      }
    }
  },
  async created() {
    //
    // 如果链接有shareId参数，则保存到localStorage
    const shareId = localStorage.getItem('shareId')
    if (!shareId) {
      this.shareId =
        process.env.NODE_ENV === 'production'
          ? 'igalbyrcd9wrbpxmhyndaw1s'
          : '4lu1lad9sv3q7z86mn29cbw3'
      localStorage.setItem('shareId', this.shareId)
    } else {
      this.shareId = shareId
    }
    this.outLinkUid = checkOutLinkUid()
    // 调用电投壹授权接口1、2实现wx.config 和 wx.agentConfig 配置，为wx.invoke接口调用做准备
    // const url = window.location.href.split('#')[0]
    const url = encodeURIComponent(window.location.href.split('#')[0])
    // let url = ''
    this.isIOSDevice = /iPhone|iPad|iPod/i.test(navigator.userAgent)
    // if (this.isIOSDevice) {
    //   url = encodeURIComponent(window.location.href.replace(location.hash, ''))
    // } else {
    //   url = encodeURIComponent(window.location.href)
    // }

    // const url = 'https://reasoningai.elinkpoc.spic.com.cn'
    // console.log('url:' + url)
    await getSignature({ url })
      .then((getSign) => {
        if (!getSign) return
        const { corpId, timestamp, nonceStr, signature } = getSign
        // console.log('getSignature:' + JSON.stringify(getSign))
        window.wx.config({
          beta: true,
          debug: false,
          appId: corpId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: [
            'hideOptionMenu',
            'chooseMessageFile',
            'getLocalFileData',
            'getCurUserExternalContact'
          ]
        })
        // 接口处理成功验证
        window.wx.ready(() => {
          getAgentTicket({ url }).then((getAgentSign) => {
            if (!getAgentSign) return
            const { timestamp, nonceStr, signature, agentId, corpId } = getAgentSign
            console.log('getAgentTicket:' + JSON.stringify(getAgentSign))
            window.wx.agentConfig({
              agentid: agentId,
              corpid: corpId,
              timestamp,
              nonceStr,
              signature,
              jsApiList: ['chooseMessageFile', 'getLocalFileData', 'getCurUserExternalContact'],
              success: (res) => {
                console.log('window.wx.agentConfig-success:' + JSON.stringify(res))
              },
              fail: (res) => {
                console.log('window.wx.agentConfig-fail:' + JSON.stringify(res))
              }
            })
          })
        })
        // 接口处理失败验证
        window.wx.error((err) => {
          console.log('wx.error接口处理失败验证:' + JSON.stringify(err))
        })
        // 隐藏右上角菜单
        window.wx.hideOptionMenu()
      })
      .catch((err) => {
        console.log('getSignature catch error:' + JSON.stringify(err))
      })
    // 调用 getInitData 接口，调用了模型那边的的 一个接口
    await getInitData().then((res) => {})

    //
  },
  mounted() {
    // window.wx.onHistoryBack((e, f) => {
    //   console.log(e, f)
    //   return true
    // })
    // alertTest(true)
    this.initPage()
    this.getUserInfo()
    this.getUserName()
    this.getInteractionTotal()
    // 监听积分刷新事件
    eventBus.$on('refreshPoints', () => {
      this.getUserInfo()
    })
  },
  beforeDestroy() {
    // 移除事件监听器
    eventBus.$off('refreshPoints')
    // 清理滚动定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
      this.scrollTimer = null
    }
  },
  methods: {
    handleFullResponseContent(content) {
      // 对于 V3 AI 一次性返回的完整内容，我们将其分段处理
      if (content && content.length > 0) {
        // 按照一定长度分段，避免一次性渲染太多内容
        const chunkSize = 10 // 每次处理10个字符
        for (let i = 0; i < content.length; i += chunkSize) {
          const chunk = content.substring(i, i + chunkSize)
          this.renderQueue.push(chunk)
        }

        // 启动渲染过程
        if (!this.isRendering) {
          this.renderText()
        }
      }
    },
    // 添加处理完整内容的方法
    processFullContent(content) {
      // 将完整内容放入渲染队列
      this.renderQueue = content.split('')
      // 启动字符级渲染
      if (!this.isRendering) {
        this.renderText()
      }
    },
    // 重写 SSE 响应处理方法，确保 V3 AI 的完整响应也能逐字符渲染
    onSseMessage(data) {
      // 检查是否是 V3 AI 模型且返回了完整内容
      if (this.llm_type === 1 && data && data.content && !this.isRendering) {
        // 如果是一次性返回的完整内容，使用字符级渲染
        this.processFullContent(data.content)
      } else {
        // 保持原有的逐步渲染逻辑
        if (data.content) {
          this.renderQueue.push(...data.content)
        }
        if (!this.isRendering) {
          this.renderText()
        }
      }
    },
    /** 文档快捷操作 */
    handleDocument(text) {
      this.showTranslateBar = false
      this.textarea = text
      this.textList = []
      // 设置功能标题和对应的msgType
      this.funTitle = text
      if (MSG_TYPE[text]) {
        this.msgType = MSG_TYPE[text]
      } else {
        this.msgType = 'common'
      }
      console.log('handleDocument设置:', 'funTitle:', this.funTitle, 'msgType:', this.msgType)
    },
    // 互动次数查询
    async getInteractionTotal() {
      try {
        const res = await getInteractionTotalRequest()
        if (res.code === 200) {
          this.interactionTotal = res.data
        }
      } catch (error) {
        console.error('获取交互总数失败:', error)
        this.interactionTotal = 0
      } finally {
        this.loading = false
      }
    },
    /** 初始化AI消息回复状态 */
    initAIMsg() {
      this.isThinking = false
    },
    // type 对应的filePopover弹框组件名字参数
    // 关闭文件选择弹窗
    triggerUpload(type) {
      // 当type为5时（发送按钮旁边的上传），允许上传图片
      if (type === 5) {
        this.ifImageTypes = true
      } else {
        // 对于其他类型的功能，重置为false，确保不会影响其他上传功能
        this.ifImageTypes = false
      }
      setTimeout(() => {
        this.$refs[`filePopover${type}`].showPopper = false
      }, 1000)
    },
    // 点击发送按钮左侧的上传
    // 清空现有的文本内容
    changeImageTypes() {
      // console.log('changeImageTypes')
      this.funTitle = ''
      this.textarea = ''
      this.textList = []
      this.ifImageTypes = true
      this.msgType = 'common' // 重置msgType
    },
    beforeAvatarUpload(file) {
      let fileType = FILE_TYPE
      // 中英互译功能允许 jpg
      // 只有在发送按钮旁边的上传（type=5）或者中英互译功能时才允许上传图片
      if (this.ifImageTypes || this.funTitle === '中英互译') {
        fileType = [...FILE_TYPE, 'jpg', 'png', 'jpeg']
      }
      // console.log('beforeAvatarUpload', this.ifImageTypes, fileType)
      const list = file.name.split('.')
      const fileSize = 10
      if (!fileType.includes(list[list.length - 1])) {
        // this.$message.error('上传文件格式不正确')
        this.$message.closeAll()
        this.$message.warning({ message: '上传文件格式不正确', duration: 3000 })
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        // this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        this.$message.closeAll()
        this.$message.warning({ message: `上传文件大小不能超过 ${fileSize}MB!`, duration: 3000 })
        return false
      }
      return isLt2M
    },
    pickerHistoryType() {
      this.historyTypeList = [
        {
          label: '置顶',
          value: 'top'
        },
        {
          label: '今日',
          value: 'today'
        },
        {
          label: '昨日',
          value: 'yesterday'
        },
        {
          label: '最近7天',
          value: 'last7Days'
        },
        {
          label: '历史',
          value: 'history'
        }
      ].map((item) => {
        return {
          title: item.label,
          children: this.chatHistoryList.filter((el) => el.timeCategory === item.value)
        }
      })
      // console.log(this.historyTypeList, 'historyTypeList')
    },
    addUsedShortcutInCurSessionNumber() {
      this.usedShortcutInCurSessionNumber = 0

      this.msgList.forEach((item) => {
        if (item.obj === 'Human' && item.value && item.value.length > 0) {
          item.value.forEach((element) => {
            if (element.type === 'text' && element.text && element.text.content) {
              const text = element.text.content.trim()
              const arr = ['文档校对', '文档总结', '文档阅读', '提示词帮助']
              if (arr.includes(text)) {
                this.usedShortcutInCurSessionNumber = this.usedShortcutInCurSessionNumber + 1
              }
            }
          })
        }
      })
    },
    async initPage() {
      // 在获取历史记录前 可能没有staffid 要专门处理一下

      const res = await getUser()
      if (res && res.code === 200) {
        const userCode = res?.data?.user?.userCode
        const resData = await getAddEncryptUserInfo({
          userId: userCode
        })
        if (resData && resData.code === 200) {
          localStorage.setItem('outLinkUid', resData.UserId)
          localStorage.setItem('staffId', resData.staffId)
          await this.getHistoryList()
          if (this.chatHistoryList.length) {
            const activeId = localStorage.getItem('activeChatId')
            const activeIndex = this.chatHistoryList.findIndex((item) => item.chatId === activeId)
            if (activeIndex === -1) {
              localStorage.removeItem('activeChatId')
              this.activeChatListIndex = 0
              this.activeChatId = this.chatHistoryList[0].chatId
              localStorage.setItem('activeChatId', this.activeChatId)
              await this.handleChatItemClick(0)
              return
            }
            if (activeId) {
              this.activeChatId = activeId
              await this.handleChatItemClick(activeIndex)
            } else {
              if (this.chatHistoryList.length) {
                this.activeChatId = this.chatHistoryList[0].chatId
                await this.handleChatItemClick(0)
              }
            }
          }
        }
      }
    },
    // 关闭历史记录列表
    handleClose(done) {
      done()
    },
    // 打开历史记录列表
    async openHistoryList() {
      await this.getHistoryList()
      this.drawer = true
    },
    // 历史记录列表点击
    async handleChatItemClick(index, item) {
      if (this.eventSourceOpen) {
        this.stopFetchSource()
      }
      if (!item) {
        this.activeChatListIndex = index
        this.activeChatId = this.chatHistoryList[index].chatId
      } else {
        this.activeChatId = item.chatId
        this.activeChatListIndex = this.chatHistoryList.findIndex((el) => el.chatId === item.chatId)
      }
      this.getChatMsgList()
    },

    /** 获取消息 */
    async getChatMsgList() {
      await getPaginationRecords({
        appId: this.appId,
        chatId: this.activeChatId,
        offset: 0,
        pageSize: 500
      }).then((res) => {
        if (res.code === 200) {
          this.drawer = false
          this.isChatInfoNull = false
          this.msgList = [...res.data.list]
          this.addUsedShortcutInCurSessionNumber()
          // console.log(this.msgList, '看看')
        }
        setTimeout(() => {
          this.scrollToBottom()
        }, 100)
      })
    },
    // 抽屉更多操作处理
    handleCommand(command, item) {
      const index = this.chatHistoryList.findIndex((el) => el.chatId === item.chatId)
      // 根据不同的command执行不同的操作
      switch (command) {
        case 'toTop':
          this.topChat(index)
          break
        case 'editTitle':
          this.editingChatId = this.chatHistoryList[index].chatId
          this.dialogVisible = true
          this.newChatTitle =
            this.chatHistoryList[index].customTitle || this.chatHistoryList[index].title
          this.ifChatTitleTop = this.chatHistoryList[index].top
          break
        case 'delete':
          this.deleteChatHistory(index)
          break
        default:
      }
    },
    // 顶部操作
    handleTopCommand(command) {
      switch (command) {
        case 'newChat':
          this.initChat()
          break
        default:
      }
    },
    // 添加文件选择后的处理逻辑
    handleFileChange(file) {
      if (this.imageFileExtensions.includes(file.raw.type.split('/')[1])) {
        const reader = new FileReader()
        reader.onload = (e) => {
          file.previewUrl = e.target.result
        }
        reader.readAsDataURL(file.raw)
      }
    },
    onProgress(event, file, fileList) {
      console.log(666)
      this.fileList = [...fileList]
    },
    // 文件上传成功
    handleUploadSuccess(response, file, fileList) {
      // 清除之前的防抖定时器
      if (this.fileUploadDebounceTimer) {
        clearTimeout(this.fileUploadDebounceTimer)
      }

      // 设置新的防抖定时器
      this.fileUploadDebounceTimer = setTimeout(() => {
        if (parseInt(response.code) !== 200) {
          // this.$message.error(response.msg)
          this.$message.warning({ message: response.msg, duration: 3000 })
          return
        }
        this.excelBox = false
        this.fileList = fileList.map((item) => {
          // if (item.percentage === 100) return item;
          return {
            ...item
          }
        })

        // 处理完成后重置防抖定时器
        this.fileUploadDebounceTimer = null
      }, this.fileUploadDebounceDelay)
    },
    handleUploadError(err, file, fileList) {
      // 检查是否是502错误
      if (err && err.status === 502) {
        this.$message.warning({ message: '服务器内部发生错误，请您刷新一下哦~', duration: 3000 })
      } else if (err && err.status === 0) {
        // 处理网络错误或跨域问题
        this.$message.closeAll()
        this.$message.warning({ message: '网络连接异常，请检查网络后重试', duration: 3000 })
      } else {
        // 更安全地处理错误信息
        let errorMessage = '文件上传失败'
        if (err) {
          // 如果err是一个对象，尝试获取其message属性
          if (typeof err === 'object') {
            if (err.message) {
              errorMessage = err.message
            } else if (err.msg) {
              errorMessage = err.msg
            } else {
              // 尝试转换为字符串
              try {
                const errStr = JSON.stringify(err)
                if (errStr) {
                  errorMessage = errStr
                }
              } catch (e) {
                // 如果连序列化都失败，则使用默认消息
                errorMessage = '文件上传失败'
              }
            }
          } else {
            // err是字符串或其他基本类型
            errorMessage = String(err)
          }

          // 尝试解析错误信息为JSON（如果它是JSON格式）
          try {
            const errStr = errorMessage.replace(/^Error:\s*/, '')
            const errData = JSON.parse(errStr)
            errorMessage = errData.msg ? errData.msg : errData.message || errorMessage
          } catch (parseError) {
            // 如果解析失败，就使用原始错误消息
            // 不需要做任何事，errorMessage已经设置好了
          }
        }

        this.$message.closeAll()
        this.$message.warning({
          message: errorMessage,
          duration: 3000
        })
      }
      this.fileList = fileList
    },
    handleExceed(files, fileList) {
      this.$message.closeAll()
      this.$message.warning({
        message: `当前限制选择8个文件，本次选择了 ${files.length} 个文件，共选择了 ${fileList.length} 个文件`,
        duration: 3000
      })
    },
    // 删除文件
    deleteFile(index) {
      this.fileList.splice(index, 1)
      if (this.fileList.length === 0) {
        this.textarea = ''
        this.textList = []
        this.isElectronic = false
        // 当删除所有文件后，重置funTitle状态，确保知识库类型重新显示
        this.funTitle = ''
        // 重置msgType，确保bizType参数正确重置
        this.msgType = 'common'
      }
    },
    // 点击 电投壹文件 时的逻辑
    // type：5 发送按钮边上的上传按钮的电投壹上传
    async selectChatFile(type, name) {
    // 点击应该清空输入框和内容还有参数设置
      // if (type === 5) {
      //   this.funTitle = ''
      //   this.textarea = ''
      //   this.textList = ''
      // }
      this.triggerUpload(type)
      if (name != null) {
        this.funTitle = name
        // 根据功能名称设置对应的msgType
        if (MSG_TYPE[name]) {
          this.msgType = MSG_TYPE[name]
        } else {
          this.msgType = 'common'
        }
        // 只清空提示词帮助的样式，保持功能名称显示
        this.textList = []
        // 如果当前没有显示功能名称，则设置功能名称
        if (!this.textarea || this.textarea !== name) {
          this.textarea = name
        }
      }

      window.wx.invoke(
        'chooseMessageFile',
        {
          count: 1,
          type: 'file'
        },
        (res) => {
          if (res.err_msg === 'chooseMessageFile:ok') {
            if (res.tempFiles && res.tempFiles.length > 0) {
              // const file = res.tempFiles[0]
              // const mimeType = getMimeType(file.name)
              // window.wx.invoke(
              //   'getLocalFileData',
              //   { localId: file.localId },
              //   (res) => {
              //     if (res.err_msg === 'getLocalFileData:ok') {
              //       const localData = res.localData
              //       const Base64Data = `data:${mimeType};base64,${localData}`
              //       const newFile = base64ToFile(Base64Data, file.name)
              //       this.$refs.uploader.handleStart(newFile)
              //       this.$refs.uploader.submit()
              //     }
              //   }
              // )
              res.tempFiles.forEach((file) => {
                const mimeType = getMimeType(file.name)
                window.wx.invoke('getLocalFileData', { localId: file.localId }, (res) => {
                  if (res.err_msg === 'getLocalFileData:ok') {
                    const localData = res.localData
                    const Base64Data = `data:${mimeType};base64,${localData}`
                    const newFile = base64ToFile(Base64Data, file.name)
                    this.isElectronic = true // 是电投宜上传
                    this.$refs.uploader.handleStart(newFile)
                    this.$refs.uploader.submit()
                  }
                })
              })
            }
          }
        }
      )
    },
    // 通过新建对话的按钮初始化聊天
    async initChat() {
      this.stopFetchSource()
      this.usedShortcutInCurSessionNumber = 0
      this.activeChatId = generateChatId(24)
      this.initAIMsg()
      this.ExcelModel = false
      this.excelBox = false
      // 重置知识库为"不使用知识库"
      this.knowledgeCode = 'SPIC-NON'
      console.log('新会话：重置知识库为不使用知识库')
      await chatInit1({
        appId: this.appId,
        chatId: this.activeChatId
      }).then((res) => {
        if (res.code === 200) {
          this.isChatInfoNull = false
          this.drawer = false
          this.activeChatListIndex = 0
          this.msgList = []
        }
      })
    },
    // 获取历史记录
    async getHistoryList() {
      await getHistories({
        offset: 0,
        pageSize: 500,
        appId: this.appId,
        source: 'online'
      }).then((res) => {
        if (res.code === 200) {
          this.chatHistoryList = res.data.list
        }
      })
      if (this.chatHistoryList.length) {
        this.activeChatListIndex = this.chatHistoryList.findIndex(
          (item) => item.chatId === this.activeChatId
        )
      }
    },
    // 删除历史记录
    async deleteChatHistory(index) {
      const deleteChatId = this.chatHistoryList[index].chatId
      if (deleteChatId === this.activeChatId && this.chatHistoryList.length > 1) {
        await delHistory({
          appId: this.appId,
          chatId: deleteChatId
        }).then((res) => {
          if (res.code === 200) {
            this.initChat()
          }
        })
        this.handleChatItemClick(0)
      } else {
        await delHistory({
          appId: this.appId,
          chatId: deleteChatId
        }).then((res) => {
          if (res.code === 200) {
            return this.getHistoryList()
          }
        })
      }
      if (this.chatHistoryList.length == 0) {
        this.initChat()
      }
    },
    // 置顶历史记录
    async topChat(index) {
      const top = this.chatHistoryList[index].timeCategory !== 'top'
      await updateHistory({
        appId: this.appId,
        chatId: this.chatHistoryList[index].chatId,
        customTitle: this.chatHistoryList[index].customTitle || this.chatHistoryList[index].title,
        top
      }).then((res) => {
        if (res.code === 200) {
          this.getHistoryList()
        }
      })
    },
    // 修改历史记录的标题
    modifyChatTitle() {
      updateHistory({
        appId: this.appId,
        chatId: this.editingChatId,
        customTitle: this.newChatTitle,
        top: this.ifChatTitleTop
      })
        .then((res) => {
          if (res.code === 200) {
            this.getHistoryList()
          }
        })
        .finally(() => {
          this.dialogVisible = false
        })
    },
    renderText() {
      this.isRendering = true
      const startTime = performance.now()
      let renderedCount = 0
      const targetIndex = this.msgList.length - 1

      // 安全检查：确保消息列表和消息结构存在
      if (targetIndex < 0 || !this.msgList[targetIndex] || !this.msgList[targetIndex].value) {
        console.error('renderText: 消息结构不完整', {
          targetIndex,
          msgListLength: this.msgList.length,
          hasMessage: !!this.msgList[targetIndex],
          hasValue: !!(this.msgList[targetIndex] && this.msgList[targetIndex].value)
        })
        this.isRendering = false
        return
      }

      const targetMessage = this.msgList[targetIndex]

      // 确保value数组有足够的元素，如果没有则创建
      if (!targetMessage.value[0]) {
        targetMessage.value[0] = {
          type: 'reasoning',
          reasoning: {
            content: ''
          }
        }
      }
      if (!targetMessage.value[1]) {
        targetMessage.value[1] = {
          type: 'text',
          text: {
            content: ''
          }
        }
      }

      // 确保reasoning和text对象存在
      if (!targetMessage.value[0].reasoning) {
        targetMessage.value[0].reasoning = { content: '' }
      }
      if (!targetMessage.value[1].text) {
        targetMessage.value[1].text = { content: '' }
      }

      // 动态调整渲染速度：根据快速渲染模式和队列长度
      const totalQueueLength = this.renderQueue.length + this.renderQueueReasoning.length
      let maxCharsPerFrame = 15 // 默认每帧15个字符
      let maxTimePerFrame = 8 // 默认每帧8ms

      // 快速渲染模式：基于SSE流速度检测，而不仅仅是队列长度
      if (this.fastRenderMode) {
        // 根据队列长度动态调整，但有最小保障
        maxCharsPerFrame = Math.min(50, Math.max(25, totalQueueLength / 5)) // 快速模式最少25个字符
        maxTimePerFrame = 16 // 增加到16ms
        console.log(`快速渲染模式: 队列长度=${totalQueueLength}, 每帧字符=${maxCharsPerFrame}`)
      } else if (totalQueueLength > 100) {
        // 即使不是快速模式，队列积压时也要适当加速
        maxCharsPerFrame = Math.min(30, Math.max(15, totalQueueLength / 8))
        console.log(`队列积压加速: 队列长度=${totalQueueLength}, 每帧字符=${maxCharsPerFrame}`)
      }

      // 处理思考过程和正文内容 - 使用统一的性能保护逻辑
      while ((this.renderQueueReasoning.length > 0 || this.renderQueue.length > 0) &&
             renderedCount < maxCharsPerFrame &&
             performance.now() - startTime < maxTimePerFrame) {
        // 优先处理思考过程
        if (this.renderQueueReasoning.length > 0) {
          const charReasoning = this.renderQueueReasoning.shift()
          targetMessage.value[0].reasoning.content += charReasoning
          renderedCount++
        }
        // 然后处理正文内容
        else if (this.renderQueue.length > 0) {
          const char = this.renderQueue.shift()
          targetMessage.value[1].text.content += char
          renderedCount++
          this.renderedContentLength += 1 // 记录已渲染字符数
        }
      }

      // 严格按累计字符数滚动：每累计渲染5个字符触发一次滚动
      if (renderedCount > 0) {
        // 计算应该滚动的次数：累计渲染字符数 / 5
        const shouldScrollTimes = Math.floor(this.renderedContentLength / 5)

        // 如果应该滚动的次数大于已经滚动的次数，则滚动
        if (!this.scrolledTimes) this.scrolledTimes = 0
        if (shouldScrollTimes > this.scrolledTimes) {
          console.log(`准备滚动: 自动滚动=${this.autoScroll}, 用户滚动=${this.userScrolling}, 应该滚动=${shouldScrollTimes}次, 已滚动=${this.scrolledTimes}次`)
          if (!this.userScrolling && this.autoScroll) {
            this.scrollToBottom('renderText-progress')
            this.scrolledTimes = shouldScrollTimes
            console.log(`累计字符滚动: 已渲染=${this.renderedContentLength}字符, 滚动次数=${this.scrolledTimes}, 队列=${totalQueueLength}`)
          } else {
            console.log(`跳过滚动: 自动滚动=${this.autoScroll}, 用户滚动=${this.userScrolling}`)
          }
        }
      }

      // 优化快速渲染模式退出逻辑：更保守的退出条件
      if (this.fastRenderMode && totalQueueLength < 5 && this.sseStreamEnded) {
        // 只有在SSE流已结束且队列几乎清空时才退出快速模式
        // 这样可以避免在渲染过程中频繁切换模式
        console.log('SSE已结束且队列基本清空，退出快速渲染模式')
        this.fastRenderMode = false
      }

      // 更新视图
      const currentQueueLength = this.renderQueue.length + this.renderQueueReasoning.length
      console.log(`渲染帧结束: 文本队列=${this.renderQueue.length}, 思考队列=${this.renderQueueReasoning.length}, 总队列=${currentQueueLength}`)

      if (this.renderQueue.length > 0 || this.renderQueueReasoning.length > 0) {
        // 继续渲染下一帧
        requestAnimationFrame(() => this.renderText())
      } else {
        // 渲染完成
        this.isRendering = false
        this.isRenderCompleted = true
        this.fastRenderMode = false // 渲染完成时重置快速渲染模式

        console.log('渲染完成，SSE流结束状态:', this.sseStreamEnded, '最终队列长度:', currentQueueLength)

        // 渲染完成后确保滚动到底部（前提是用户没有手动滚动）
        if (!this.userScrolling && this.autoScroll) {
          // 使用更可靠的渲染完成后滚动逻辑
          this.handleRenderCompleted()
        }

        // 清除滚动防抖定时器
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer)
          this.scrollTimer = null
        }
      }
    },
    flushRenderQueue() {
      if (this.renderQueue.length > 0 || this.renderQueueReasoning.length > 0) {
        const targetIndex = this.msgList.length - 1
        // 一次性添加所有剩余内容
        this.msgList[targetIndex].value[0].reasoning.content += this.renderQueueReasoning.join('')
        this.msgList[targetIndex].value[1].text.content += this.renderQueue.join('')
        this.renderQueue = []
        this.renderQueueReasoning = []

        // 确保在内容更新后滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom('flushRenderQueue')
        })
      }
    },
    // 获取精确的滚动目标位置，确保所有内容（包括点赞点踩按钮）都可见
    getAccurateScrollTarget() {
      const box = this.$refs.chatArea
      if (!box) return 0

      // 直接使用scrollHeight，这是最准确的内容高度
      // 包含了所有子元素的高度，包括点赞点踩按钮
      return box.scrollHeight - box.clientHeight
    },

    // 新增：处理渲染完成后的滚动逻辑，确保时序正确
    handleRenderCompleted() {
      console.log('渲染完成，开始处理滚动，SSE流结束状态:', this.sseStreamEnded)

      // 等待DOM完全更新，包括可能的异步组件渲染（如点赞按钮）
      this.$nextTick(() => {
        // 第一次滚动：立即滚动到当前计算的底部
        this.ensureScrollToBottom('renderCompleted-immediate')

        // 如果SSE流已结束，需要更长的延迟来等待点赞按钮等异步内容渲染
        const delayTime = this.sseStreamEnded ? 200 : 100
        const finalDelayTime = this.sseStreamEnded ? 500 : 300

        // 第二次滚动：等待可能的异步内容渲染完成
        setTimeout(() => {
          if (!this.userScrolling && this.autoScroll) {
            this.ensureScrollToBottom('renderCompleted-delayed')
          }
        }, delayTime)

        // 第三次滚动：最终保障，确保所有内容都已渲染
        setTimeout(() => {
          if (!this.userScrolling && this.autoScroll) {
            this.ensureScrollToBottom('renderCompleted-final')
            console.log('最终滚动完成')
          }
        }, finalDelayTime)

        // 额外的保障：如果内容高度还在变化，继续滚动
        this.monitorContentChanges()
      })
    },

    // 新增：监控内容高度变化，确保滚动跟上内容渲染
    monitorContentChanges() {
      const box = this.$refs.chatArea
      if (!box) return

      let lastHeight = box.scrollHeight
      let checkCount = 0
      let stableCount = 0 // 连续稳定的次数
      const maxChecks = this.sseStreamEnded ? 15 : 10 // SSE结束后检查更多次
      const stableThreshold = 3 // 连续3次高度不变才认为稳定

      const checkHeightChange = () => {
        if (checkCount >= maxChecks || this.userScrolling || !this.autoScroll) {
          console.log('停止监控内容变化')
          return
        }

        const currentHeight = box.scrollHeight
        if (currentHeight !== lastHeight) {
          console.log(`内容高度变化: ${lastHeight} -> ${currentHeight}`)
          lastHeight = currentHeight
          stableCount = 0 // 重置稳定计数
          this.ensureScrollToBottom('height-change-detected')

          // 继续监控
          setTimeout(checkHeightChange, 100)
        } else {
          stableCount++
          if (stableCount >= stableThreshold) {
            console.log('内容高度已稳定，停止监控')
            return
          }
          // 高度未变化，继续监控但间隔稍长
          setTimeout(checkHeightChange, 150)
        }
        checkCount++
      }

      // 开始监控
      setTimeout(checkHeightChange, 50)
    },

    // 新增：智能滚动方法，确保内容渲染完成后能正确滚动到底部
    ensureScrollToBottom(source = 'default', delay = 0) {
      if (!this.autoScroll || this.userScrolling) {
        return
      }

      const performScroll = () => {
        const box = this.$refs.chatArea
        if (box) {
          // 使用精确的滚动目标位置，确保包括点赞点踩按钮在内的所有内容都可见
          const targetScrollTop = this.getAccurateScrollTarget()

          // 立即滚动到精确位置
          box.scrollTop = targetScrollTop

          // 使用多重保障确保滚动到位
          this.$nextTick(() => {
            if (!this.userScrolling) {
              // 重新计算目标位置，因为DOM可能已更新
              const newTargetScrollTop = this.getAccurateScrollTarget()
              box.scrollTop = newTargetScrollTop

              // 最终保障
              setTimeout(() => {
                if (!this.userScrolling && this.autoScroll) {
                  const finalTargetScrollTop = this.getAccurateScrollTarget()
                  box.scrollTop = finalTargetScrollTop
                }
              }, 50)
            }
          })
        }
      }

      if (delay > 0) {
        setTimeout(performScroll, delay)
      } else {
        performScroll()
      }
    },
    scrollToBottom(source) {
      console.log('滑到底部', source, '自动滚动:', this.autoScroll, '用户滚动:', this.userScrolling)
      const box = this.$refs.chatArea
      if (box) {
        // 只有在允许自动滚动且用户没有手动滚动时才滚动
        if (this.autoScroll && !this.userScrolling) {
          this.jsScrolling = true

          // 使用精确的滚动逻辑，确保包括点赞点踩按钮在内的所有内容都可见
          const scrollToActualBottom = () => {
            const targetScrollTop = this.getAccurateScrollTarget()

            // 第一次滚动到精确位置
            box.scrollTop = targetScrollTop

            // 确保在下一帧再次滚动以处理可能的内容更新
            requestAnimationFrame(() => {
              const newTargetScrollTop = this.getAccurateScrollTarget()
              box.scrollTop = newTargetScrollTop

              // 再次检查并滚动，确保到底部
              setTimeout(() => {
                const finalTargetScrollTop = this.getAccurateScrollTarget()
                box.scrollTop = finalTargetScrollTop
                this.jsScrolling = false
              }, 10)
            })
          }

          // 对于大量数据，可能需要延迟执行以确保DOM更新完成
          if (source && source.includes('renderCompleted')) {
            // 如果是渲染完成的调用，增加延迟确保DOM完全更新
            setTimeout(() => {
              scrollToActualBottom()
            }, 30)
          } else {
            // 正常情况立即执行
            scrollToActualBottom()
          }
        }
      }
    },
    transAudioSuccess(data) {
      // this.textarea = data
      this.textList = []
    },
    // 处理子组件textarea变化事件
    handleTextareaChange(newVal, oldVal) {
      console.log('父组件接收到textarea变化:', 'newVal:', newVal, 'oldVal:', oldVal)
      if (newVal.length === 0) {
        // 检查是否是提示词帮助状态
        const wasPromptHelp = this.funTitle === '提示词帮助'
        this.textList = []
        console.log('handleTextareaChange1', this.funTitle)

        // 只有在以下情况下才重置状态：
        // 1. 之前是提示词帮助状态，且没有文件
        // 2. 不是持续性功能（如excel助手）的状态
        const isPersistentFunction = this.ExcelModel || this.funTitle === 'excel助手'

        if (wasPromptHelp && this.fileList.length === 0 && !isPersistentFunction) {
          this.funTitle = ''
          this.msgType = 'common'
        }
        console.log('handleTextareaChange2', this.funTitle, 'isPersistentFunction:', isPersistentFunction)
      }
    },
    sendComMsgRightReSubmit(row) {
      console.log(row)
      const startIndex = this.msgList.findIndex((item) => item.dataId === row.dataId)
      console.log(startIndex)
      const delList = this.msgList.filter((item, index) => startIndex <= index)
      delList.forEach((element) => {
        itemDelete({
          appId: this.appId,
          chatId: this.activeChatId,
          contentId: element.dataId
        })
      })
      this.msgList = this.msgList.slice(0, startIndex)

      const msg = new FileMsgClass(row.dataId, 'Human', '', this.fileList, row.value)
      this.msgList.push(msg)
      setTimeout(() => {
        const params = new FileCompletions(
          row.dataId,
          this.textarea,
          this.appId,
          this.activeChatId,
          localStorage.getItem('shareId'),
          localStorage.getItem('outLinkUid'),
          '',
          row.value
        )
        this.sseReq(params)
      }, 2000)
    },
    sendComMsgRightDel(msg) {
      const index = this.msgList.findIndex((item) => item.dataId === msg.dataId)
      itemDelete({
        appId: this.appId,
        chatId: this.activeChatId,
        contentId: this.msgList[index + 1].dataId
      })
        .then((res) => {
          return getPaginationRecords({
            appId: this.appId,
            chatId: this.activeChatId,
            pageNum: 1,
            pageSize: 10
          })
        })
        .then((res) => {
          if (res.code === 200) {
            this.drawer = false
            this.isChatInfoNull = false
            this.msgList = res.data.list
            this.addUsedShortcutInCurSessionNumber()
          }
        })
    },
    // 编辑 实际上就是把传过来的值丢给输入框
    sendComMsgRightEdit(text) {
      this.textarea = text
      // 输入框获取焦点
      this.$nextTick(() => {
        this.$refs.mobileAudio.focusInput()
      })
    },

    handleScroll(e) {
      const { scrollTop, clientHeight, scrollHeight } = e.target
      const threshold = 50 // 距离底部的阈值
      // 判断是否接近底部
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - threshold
      // 更新 isAtBottom 状态
      this.isAtBottom = isAtBottom
      // 只有在接近底部时才重置 userScrolling 状态
      if (isAtBottom) {
        this.userScrolling = false
        this.showBackToBottom = false
      } else if (!this.jsScrolling) {
        // 如果不在底部且不是JS正在滚动，则标记为用户手动滚动
        this.userScrolling = true
        this.showBackToBottom = true
      }
    },
    forceScrollToBottom() {
      this.userScrolling = false
      this.jsScrolling = true
      this.showBackToBottom = false
      this.autoScroll = true // 确保自动滚动开启

      const box = this.$refs.chatArea
      if (box) {
        // 立即滚动到精确的底部位置
        const targetScrollTop = this.getAccurateScrollTarget()
        box.scrollTop = targetScrollTop

        // 使用多重保障确保滚动到位
        this.$nextTick(() => {
          requestAnimationFrame(() => {
            // 重新计算并滚动到精确位置
            const newTargetScrollTop = this.getAccurateScrollTarget()
            box.scrollTop = newTargetScrollTop

            // 添加多重保障确保滚动到底部
            const scrollToBottom = () => {
              const finalTargetScrollTop = this.getAccurateScrollTarget()
              box.scrollTop = finalTargetScrollTop

              // 验证滚动是否到位
              const currentScrollTop = box.scrollTop
              if (Math.abs(currentScrollTop - finalTargetScrollTop) > 1) {
                box.scrollTop = finalTargetScrollTop
              }
            }

            scrollToBottom()
            // 添加延时保障
            setTimeout(() => {
              scrollToBottom()
              this.jsScrolling = false

              // 最终检查
              setTimeout(() => {
                const finalTargetScrollTop = this.getAccurateScrollTarget()
                if (Math.abs(box.scrollTop - finalTargetScrollTop) > 1) {
                  box.scrollTop = finalTargetScrollTop
                }
              }, 100)
            }, 50)
          })
        })
      }
    },
    assistantClick() {
      this.funTitle = '提示词帮助'
      this.msgType = 'common' // 重置msgType，确保状态正确
      this.$refs.comAssistance.show()
    },
    sendComAssistance(row) {
      this.textarea = row.map((item) => item.text).join('')
      this.textList = [...row]
    },
    comAssistanceClose(flag) {
      // console.log('comAssistanceClose', this.textList)
      if (this.textList.length > 0) {
        this.funTitle = '提示词帮助'
        this.msgType = MSG_TYPE[this.funTitle] || 'common' // 使用MSG_TYPE常量设置msgType
      } else {
        this.funTitle = ''
        this.msgType = 'common' // 重置为默认msgType
      }
    },
    // 点击 本地文件 时的逻辑
    // type:5的清空逻辑在changeImageTypes()中
    handleExternalClick(type, name, imageFlage) {
      // 上传本地文件
      this.isElectronic = false
      // console.log('handleExternalClick', type, name, imageFlage)
      if (name != null) {
        this.funTitle = name
        // 根据功能名称设置对应的msgType
        if (MSG_TYPE[name]) {
          this.msgType = MSG_TYPE[name]
        } else {
          this.msgType = 'common'
        }
        // 只清空提示词帮助的样式，保持功能名称显示
        this.textList = []
        // 如果当前没有显示功能名称，则设置功能名称
        if (!this.textarea || this.textarea !== name) {
          this.textarea = name
        }
      }
      this.$refs.uploader.$refs['upload-inner'].handleClick() // 触发文件选择弹窗[1]()
      this.triggerUpload(type)
    },
    stopReading() {
      this.isReading = false
    },
    handleSpeedChange(speed) {
      this.currentSpeed = speed
      const comMsgLeftList = this.$refs.comMsgLeftRef
      comMsgLeftList.forEach((item) => {
        if (item.isReading) {
          item.speedChange(speed)
        }
      })
    },
    llm_change(llm) {
      this.llm_type = llm
      this.$refs.popover1.doClose()
    },
    // 去意见反馈页面
    feedbackClick() {
      this.$router.push({
        path: '/mobilefeedback'
      })
    },
    // 去积分页面
    userInfoClick() {
      this.$router.push({
        path: '/mobilepoints'
      })
    },
    // 获取用户积分信息
    async getUserInfo() {
      const res = await userbehavior({ staffId: this.staffId })
      // console.log(res)
      this.userInfo = res.data
    },
    // 获取用户姓名
    async getUserName() {
      const res = await getSysuser(this.staffId)
      // console.log(res)
      this.userName = res.data?.userName || '--'
    },
    // excel助手按钮
    excelHelpClick() {
      this.ExcelModel = true
      this.excelBox = true
      this.funTitle = 'excel助手'
      this.msgType = 'excel' // 使用MSG_TYPE常量设置msgType
      // 清空提示词帮助的内容和样式
      this.textList = []
      this.textarea = ''
    },
    // 关闭excel助手
    closeExcel() {
      this.ExcelModel = false
      this.excelBox = false
      this.funTitle = ''
      this.msgType = 'common' // 重置为默认msgType
    },
    // 点击快捷excel
    async handleExcelClick(index) {
      if (this.isThinking) {
        this.sendClick('stop')
      }
      console.log('handleExcelClick', index, this.funTitle)
      let file = ''
      let text = ''
      this.funTitle = 'excel助手'
      this.msgType = 'excel'
      switch (index) {
        case 0:
          // 各分公司销售额_示例数据.xlsx
          // 帮我把文件中的所有sheet按相同列名合并到一张表中，重新生成一个excel文件
          file = './template/各分公司销售额_示例数据.xlsx'
          text = '帮我把文件中的所有sheet按相同列名合并到一张表中'
          break
        case 1:
          file = './template/商品销售情况_示例数据.xlsx'
          text = '告诉我不同品类的下单次数和销售额是多少'
          // 商品销售情况_示例数据.xlsx
          // 告诉我不同品类的总下单次数和总销售额是多少
          break
        case 2:
          file = './template/人口统计_示例数据.xlsx'
          text = '统计20-29岁、30-39岁、40-49岁的女性人数占比，绘制饼图'
          // 人口统计_示例数据.xlsx
          // 统计20-29岁、30-39岁、40-49岁的女性人数占比，绘制饼图
          break
      }
      console.log(file)
      const formData = new FormData()
      const files = await this.getFile(file)
      formData.append('file', files)
      formData.append('metadata', JSON.stringify({ chatId: this.activeChatId }))
      formData.append('bucketName', 'chat')
      formData.append(
        'data',
        JSON.stringify({
          appId: this.appId,
          outLinkUid: this.outLinkUid,
          ...(this.shareId ? { shareId: this.shareId } : {})
        })
      )

      // console.log(files)
      const res = await uploadFile(formData)
      if (res.code !== 200) {
        this.$message.warning({ message: res.msg, duration: 3000 })
        return
      }
      this.textarea = text
      this.fileList.push({
        name: file.split('/').pop(),
        url: res.data,
        response: res,
        raw: files
      })
      console.log('handleExcelClick', this.funTitle)
      this.sendClick()
      this.excelBox = false
      // console.log(res)
    },
    // 获得文件流
    async getFile(filePath) {
      try {
        // 1. 发起请求获取文件二进制数据
        const response = await fetch(filePath)
        if (!response.ok) {
          throw new Error(`文件获取失败: ${response.statusText}`)
        }

        // 2. 将响应转换为Blob
        const blob = await response.blob()

        // 3. 从路径中提取文件名
        const fileName = filePath.split('/').pop()
        console.log(blob)
        // 4. 创建File对象（包含文件名和MIME类型）
        return new File([blob], fileName, {
          type: blob.type || 'application/octet-stream'
        })
      } catch (error) {
        console.error('获取文件失败:', error)
        this.$message.warning({ message: '文件获取失败，请检查路径是否正确', duration: 3000 })
        throw error // 抛出错误供调用方处理
      }
    }
  }
}
</script>

<style scoped lang="scss">
.font {
  font-family: 'Digital7', sans-serif !important;
  font-size: 52px;
  color: #4cb848;
  font-weight: 500;
  letter-spacing: 3px;
  vertical-align: -3px;
}
.loading-placeholder {
  color: #ccc;
  font-size: 40px;
}
ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
body {
  overscroll-behavior: none; /* 禁止页面溢出滚动 */
  touch-action: none; /* 禁止触摸默认行为 */
  overflow: hidden;
}
.mobile-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  width: 100vw;
  height: 100vh;
  .service-hotline {
    height: 50px;
    border-bottom: 1px solid #f2f1f4;
    background-color: #fbfbfb;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 5px 20px 5px 10px;
    .chat_history_button {
      // width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
    }
    .tip_text {
      text-align: right;
      padding: 0 5px;
      color: #6f6f6f;
      font-size: 14px;
    }
    .more_operator {
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
    }
  }

  .logo-introduction,
  .chat-area {
    position: relative;
    flex-grow: 1;
    padding: 0 15px;
    overflow-y: auto;
    overflow-x: hidden;
    .speed-select {
      position: fixed;
      right: 10px;
      bottom: 240px;
      width: 80px;
      height: 40px;
      background-color: #ebf5e8;
      border-radius: 15px;
      .act-button {
        width: 80px;
        text-align: center;
        line-height: 40px;
        color: #4cb848;
      }
    }
    .speed-select :deep(.el-popover) {
      /* 具体样式同上 */
      border-radius: 10px;
    }
  }

  .shortcut-area {
    height: 50px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .container {
      width: 100%;
      overflow-x: scroll;
      display: flex;
      flex-wrap: nowrap;
      white-space: nowrap;
      &::-webkit-scrollbar {
        display: none;
      }
      /* Firefox */
      scrollbar-width: none;
      /* 旧版 IE/Edge */
      -ms-overflow-style: none;
      span {
        display: inline-block;
      }
    }
    .content-help {
      padding:5px 10px;

      // margin-left: 10px;
      background-color: #fff;
      margin: 4px;
      box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
      display: inline-block;
      border-radius: 5px;
      line-height: 22px;
    }
  }

  .logo-introduction {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .logo img {
      height: 60px;
      padding: 5px;
    }
    .introduction {
      text-align: center;
      .ltitle {
        padding: 0 40px;
        font-size: 14px;
        color: #8c979f;
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }

  .file-preview-area {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    flex-wrap: nowrap;
    overflow: auto;
    height: 85px;
    min-height: 85px;
    width: 100vw;
    margin-top: 10px;
    padding: 0 10px 0 10px;
    .file-item {
      position: relative;
      margin-right: 8px;
      .file-preview{
        box-shadow: none;
        border: 1px solid #4CB848;
        border-radius: 10px;
      }
      .file-info {
        display: flex;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        //width: 200px;
        max-width: 200px;
        .file-name {
          flex-grow: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .el-icon-document {
          font-size: 20px;
          line-height: 40px;
        }
      }
      .delete-file {
        position: absolute;
        display: inline-block;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        z-index: 999;
        .el-icon-error{
          color:#000
        }
      }

    }
    .add-file-icon{
        width: 63px;
        height: 63px;
        background: #F3F3F3;
        margin-left: 10px;
        // border: 1px solid #ccc;
        //  padding: 10px;
        border-radius: 10px;
        line-height: 63px;
        text-align: center;
      }
  }

  .margin-bottom-ios {
    margin-bottom: 30px;
  }

  .sent-area {
    padding: 0 16px 0 10px;

    .input-line {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .msg-config-line {
      display: flex;
      flex-direction: row;
      justify-content: end;
      align-items: center;
      height: 74px;

      .model-switch {
        flex-grow: 1;
        height: 44px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .btn {
          width: 140px;
          height: 30px;
          text-align: center;
          background-color: #ebf5e8;
          line-height: 30px;
          color: #4cb848;
          border-radius: 15px;
        }
        .reference-slot {
          font-size: 14px;
        }
      }

      .upload-file,
      .spic-file,
      .sent-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 44px;
        width: 44px;
        font-size: 24px;
        line-height: 44px;
      }
    }
  }
  .sent-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    // height: 44px;
    // width: 44px;
    font-size: 24px;
    line-height: 44px;
  }
  .chat-drawer-container {
    ::v-deep .el-drawer__header {
      margin-bottom: 10px;
      display: block;
      .chat-title {
        font-weight: 700;
        font-size: 20px;
        color: #262626;
        margin-bottom: 15px;
      }
      .new-chat {
        height: 30px;
        display: inline-block;
        padding: 0 10px;
        border-radius: 20px;
        color: var(--color-primary);
        text-align: center;
        line-height: 30px;
        background: var(--color-primary-light);
      }
    }

    ::v-deep .el-drawer__body {
      width: 100%;

      .chat-list-container {
        padding: 20px 0px 20px 16px;
        height: calc(100% - 150px);
        overflow: hidden;
        overflow-y: auto;
      }
      .chat-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: start;
        width: 100%;
        .chat-item {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          width: 90%;
          height: 40px;
          padding: 0 5px;
          margin-top: 10px;
          border-radius: 8px;
          .chat-title {
            width: 70%;
            color: #6a6d74;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
          .start-time {
            width: 50px;
            font-size: 14px;
            color: #727578;
          }
          .operator {
            width: 35px;
            span {
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
        .chat-item.active {
          background-color: var(--color-primary-light);
          .chat-title {
            color: var(--color-primary);
          }
        }
      }
      .chatBottom {
        padding: 0 30px;
      }
      .line {
        height: 1px;
        background-color: #dcdcdc;
        margin: 20px 0;
        width: 100%;
      }
      .feedbackBtn {
        height: 30px;
        margin: 0 auto;
        border-radius: 10px;
        color: var(--color-primary);
        text-align: center;
        line-height: 30px;
        background: var(--color-primary-light);
        display: flex;
        align-items: center;
        justify-content: center;
        .svg-icon {
          font-size: 24px;
        }
      }
      .userInfo {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        .userPic {
          img {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            box-shadow: 0px 4px 4px 0px #dcdddf;
          }
          .info {
            margin-left: 8px;
            .userName {
              color: #343435;
              white-space: nowrap;
            }
            .total {
              color: var(--color-primary);
              .total-title {
                margin-top: 2px;
              }
            }
          }
        }
        .right {
          svg {
            font-size: 26px;
          }
        }
      }
    }
  }

  ::v-deep .el-dialog {
    border-radius: 8px;
  }

  ::v-deep .el-dialog__body {
    padding: 0 20px;
  }

  .progress {
    background-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
  }
}

.document-summary-list {
  li {
    height: 40px;
    font-size: 16px;
    line-height: 40px;
    &:nth-child(-n + 1) {
      border-bottom: 1px solid #dcdcdc;
    }
  }
}

.speed-list {
  li {
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  li:nth-child(-n + 3) {
    border-bottom: 1px solid #dcdcdc;
  }
}
.llm-list {
  li {
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    &:nth-child(1) {
      border-bottom: 1px solid #dcdcdc;
    }
  }
}
.sp-deepSeek {
  font-size: 60px;
}
.edit-box {
  // padding: 0 10px;
  box-shadow: 0px 0px 10.7px 2px rgba(0, 0, 0, 0.25);
  width: 98%;
  margin: 0 auto;
  border-radius: 30px 30px 0 0;
  padding: 10px 0;
  .line {
    margin-top: 10px;
    margin-bottom: 10px;
    border-top: 1px solid #7272723d;
    transform: scaleY(0.5); /* 压缩高度至0.5px */
  }
}
.edit-box-2 {
  box-shadow: 0px -7px 12.7px -1px rgba(0, 0, 0, 0.1);
  width: 100%;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  padding: 10px 0;
}
.excelHandel {
  position: relative;
  .title {
    padding: 15px 25px 10px 25px;
    // color: #4cb848;
    color:#090909;
    font-family: Microsoft YaHei UI;
    font-weight: 400;
    // font-size: 32px;
  }

  .close-excel-handel {
    position: absolute;
    right: 4px;
    top: -5px;
    padding: 20px;
    box-sizing: border-box;
    color:#727272;
  }
}
</style>
<style lang="scss" scoped>
.icon-top {
  font-size: 24px;
}
.chat-title1 {
  font-weight: 700;
  font-size: 16px;
  color: #555;
  margin: 20px 0 0 0;
}
.think-btn {
  font-size: 30px;
  color: var(--color-primary);
}
/* 强制覆盖所有状态下的颜色 */
.no-color-change,
.no-color-change:hover,
.no-color-change:active,
.no-color-change:focus {
  color: #606266 !important;
}

/* 覆盖Element UI上传组件的默认样式 */
.el-upload .el-upload--text span,
.el-upload .el-upload--text span:hover,
.el-upload .el-upload--text span:active,
.el-upload .el-upload--text span:focus {
  color: #606266 !important;
}

/* 确保SVG图标也不会变色 */
.no-color-change .svg-icon {
  color: inherit !important;
}
.translate-bar {
  margin-left: 10px;
}
.back-to-bottom-container {
  position: sticky;
  bottom: 20px;
  display: flex;
  justify-content: center;
  z-index: 10;
  pointer-events: none;
}

.back-to-bottom-btn {
  pointer-events: auto;
  background-color: #ffffff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #efefef !important;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: opacity 0.3s;
}

.arrow-icon {
  font-size: 20px;
  color: #606266;
}
</style>
