import { generateChatId } from '@/utils/deepseek'

class FileCompletions {
  constructor(dataId, content, appId, chatId, shareId, outLinkUid, fileList, historyList, translateFrom, translateTo) {
    const
      imageFileExtensions = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'bmp',
        'svg',
        'webp',
        'tiff',
        'tif',
        'psd',
        'raw',
        'heic',
        'heif',
        'avif',
        'ico',
        'cur',
        'apng',
        'pjpeg',
        'pjp'
      ]
    let _content = []
    if (fileList) {
      _content = [
        ...fileList.map(item => {
          if (imageFileExtensions.includes(item.raw.type.split('/')[1])) {
            return {
              'type': 'image_url',
              'image_url': {
                'url': item.response.data.previewUrl
              }
            }
          } else {
            return {
              'type': 'file_url',
              'name': item.name,
              'url': item.response.data.previewUrl
            }
          }
        })
      ]
    }
    if (content) {
      _content = [..._content, {
        type: 'text',
        text: content
      }]
    }
    if (historyList) {
      _content = historyList.map(item => {
        if (item.type === 'text') {
          return {
            type: 'text',
            text: item.text.content
          }
        } else if (item.type === 'file') {
          if (item.file.type === 'file') {
            return {
              'type': 'file_url',
              'name': item.file.name,
              'url': item.file.url
            }
          } else if (item.file.type === 'image') {
            return {
              'type': 'image_url',
              'image_url': {
                'url': item.file.url
              }
            }
          }
        }
      })
    }
    this.messages = [
      {
        dataId: dataId,
        hideInUI: false,
        role: 'user',
        content: _content
      }
    ]

    this.variables = {
      cTime: this._getCurrentTime()
    }
    // 添加翻译相关参数
    // if (translateFrom) {
    //   this.variables.translateFrom = translateFrom
    // }
    // if (translateTo) {
    //   this.variables.translateTo = translateTo
    // }

    this.responseChatItemId = generateChatId(24)
    // this.appId = appId
    this.chatId = chatId
    this.shareId = shareId
    this.outLinkUid = outLinkUid
    this.detail = true
    this.stream = true
    // this.translateFrom = translateFrom
    // this.translateTo = translateTo
  }

  // 获取格式化的当前时间
  _getCurrentTime() {
    const date = new Date()

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const dayName = days[date.getDay()]

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${dayName}`
  }

  // 可选：获取完整请求对象
  getRequestObject() {
    return {
      messages: this.messages,
      variables: this.variables,
      responseChatItemId: this.responseChatItemId,
      appId: this.appId,
      chatId: this.chatId,
      detail: this.detail,
      stream: this.stream,
      translateFrom: this.translateFrom,
      translateTo: this.translateTo
    }
  }
}

export default FileCompletions

// 使用示例
// const completion = new TextCompletions(
//   "snwKP8pyQcBqSbOene2hVcDv",
//   "我测试一下提问接口",
//   "67889533a13c9cb2f3b2a708",
//   "faPQ8E8BpG
