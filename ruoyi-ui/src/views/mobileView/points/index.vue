<!-- 基础框架 -->
<template>
  <div>
    <div class="points">
      <div class="points-item" @click="goToGetPoints">
        <div class="left">
          <svg-icon icon-class="points" />
          <span>获取积分</span>
        </div>
        <div class="right">
          <svg-icon icon-class="arrow-right" />
        </div>
      </div>
      <div class="points-item" @click="goToPointsRank">
        <div class="left">
          <svg-icon icon-class="rankpoints" />
          <span>积分排行</span>
        </div>
        <div class="right">
          <svg-icon icon-class="arrow-right" />
        </div>
      </div>
      <div class="points-item" @click="goToMyFeedback">
        <div class="left">
          <svg-icon icon-class="feedbacklist" />
          <span>我的反馈</span>
        </div>
        <div class="right">
          <svg-icon icon-class="arrow-right" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "points",
  mounted() {},
  methods: {
    goToGetPoints() {
      this.$router.push({ path: "/getpoints" });
    },
    goToPointsRank() {
      this.$router.push({ path: "/pointsrank" });
    },
    goToMyFeedback() {
      this.$router.push({ path: "/myfeedback" });
    },
  },
};
</script>

<style scoped lang="scss">
.points {
  padding: 30px;
  .points-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #aaaaaa;
    padding-bottom: 10px;
    margin-bottom: 18px;
    .left {
      display: flex;
      align-items: center;
      span {
        margin-left: 8px;
        color: #333333;
        font-size: 16px;
      }
    }
    svg {
      font-size: 22px;
    }
  }
}
</style>
