<!-- 基础框架 -->
<template>
  <div ref="myfeedback" class="myfeedback">
    <el-collapse v-model="activeName">
      <template v-for="item in list">
        <el-collapse-item :title="item.createTime" :name="item.id">
          <div style="margin-top: 10px">
            <strong>【反馈内容】</strong>：{{ item.feedbackContent }}
          </div>
          <div class="imgs">
            <el-image
              v-for="imgitem in item.imageUrls"
              style="width: 70px; height: 70px"
              :src="imgitem"
              :preview-src-list="item.imageUrls"
            />
          </div>
          <div class="backlist">
            <div v-for="sitem in item.responseContentList" class="item">
              <strong>【{{ sitem.responseDate.split(' ')[0] }}】:</strong>
              {{ sitem.responseContent }}
            </div>
            <!-- <div class="item">
              <strong>【2025年7月2日】:</strong>
              亲，很高兴收到你的反馈，我们会定位、处理问题，请耐心等待。
            </div> -->
          </div>
        </el-collapse-item>
      </template>
    </el-collapse>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else-if="!hasMore" class="no-more">加载完毕</div>
  </div>
</template>
<script>
import { getstaffId } from '@/views/mobileView/utlis'
import { getFeedbackListRequest } from '@/api/mobile.js'
export default {
  name: 'Myfeedback',
  data() {
    return {
      staffId: getstaffId(), // localStorage.getItem('staffId'), // 获取当前登录用户的id
      page: {
        pageNum: 1,
        pageSize: 20
      },
      list: [],
      totalCount: 0,
      activeName: '1',
      loading: false,
      hasMore: true,
      url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      srcList: [
        'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
      ]
    }
  },
  mounted() {
    this.getlist()
    const container = this.$refs.myfeedback
    container.addEventListener('scroll', this.handleScroll)
  },
  beforeDestroy() {
    const container = this.$refs.myfeedback
    container.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    async getlist(isLoadMore = false) {
      if (this.loading) return
      this.loading = true
      const res = await getFeedbackListRequest({
        page: this.page,
        filter: {
          userCode: this.staffId
        }
      })
      this.loading = false
      if (res.code !== 200) return this.$message.warning({ message: '获取反馈列表失败', duration: 3000 })

      if (isLoadMore) {
        this.list = [...this.list, ...res.data.dataList]
      } else {
        this.list = res.data.dataList
      }

      this.totalCount = res.data.totalCount
      this.hasMore = this.list.length < this.totalCount
      if (this.list.length > 0) {
        this.activeName = this.list[0].id
      }
      console.log(res)
    },
    handleScroll() {
      const container = this.$refs.myfeedback
      const { scrollTop, scrollHeight, clientHeight } = container
      if (
        scrollTop + clientHeight >= scrollHeight - 100 &&
        this.hasMore &&
        !this.loading
      ) {
        this.page.pageNum++
        this.getlist(true)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.myfeedback {
  padding: 30px;
  height: 100vh;
  overflow: auto;
  position: relative;
  .imgs {
    .el-image {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
  .loading,
  .no-more {
    text-align: center;
    padding: 15px;
    color: #666;
    font-size: 14px;
  }
}
::v-deep .el-collapse-item__header {
  border-bottom: 1px solid #e6ebf5 !important;
}
::v-deep .el-collapse-item__arrow {
  display: block;
}
::v-deep .el-collapse-item__content {
  border-left: none;
}
</style>
