<template>
  <div class="getPoints">
    <div class="tabs">
      <div
        class="tab-item"
        :class="{ active: activeName === 'first' }"
        @click="handleClick('first')"
      >
        积分任务
      </div>
      <div
        class="tab-item"
        :class="{ active: activeName === 'second' }"
        @click="handleClick('second')"
      >
        积分明细
      </div>
    </div>

    <!-- 积分任务内容 -->
    <div v-if="activeName === 'first'" class="tasks">
      <div v-for="(task, index) in tasks" :key="index" class="task-item">
        <div class="task-title">
          <div>
            {{ task.pointsName }}
          </div>

          <div class="task-action">
            <!-- 任务状态,-1-去完成，0-已完成，1-继续完成，2-持续完成=>继续完成 -->
            <el-tag
              v-if="task.taskStatus === -1"
              class="tags"
              style="color:#0075E3;background-color: #CCE3F9;"
              @click="completeTask(task)"
            >去完成</el-tag>
            <el-tag v-else-if="task.taskStatus === 0" class="tags" type="success">已完成</el-tag>
            <el-tag v-else-if="task.taskStatus === 1" class="tags" type="warning" @click="completeTask(task)">继续完成</el-tag>
            <el-tag v-else class="tags" type="warning" @click="completeTask(task)">继续完成</el-tag>
          </div>
        </div>
        <div class="task-description">{{ task.description }}</div>

      </div>
    </div>

    <!-- 积分明细内容 -->
    <div v-if="activeName === 'second'" ref="details" class="details">
      <!-- 积分明细的具体实现 -->
      <div class="titlt">
        <div class="detail-name" style="width: 45%;">积分名称</div>
        <div class="detail-points" style="width: 20%;">积分数量</div>
        <div class="detail-time" style="width: 35%;">时间</div>
      </div>

      <div v-for="(detail, index) in details" :key="index" class="detail-item">
        <div class="detail-name">{{ detail.pointsName }}</div>
        <div class="detail-points">{{ detail.pointsPerTransaction }}</div>
        <div class="detail-time">{{ detail.updateTime }}</div>
      </div>
      <div v-if="loading" class="loading">加载中...</div>
      <div v-if="noMoreData" class="no-more-data">没有更多数据</div>

    </div>
  </div>
</template>

<script>
// import  {getIntegralTaskListRequest,getIntegralDetailRequest} from '@/api/mobile'
import { tasklist, pointsdetails } from '@/api/points'
import { getstaffId } from '@/views/mobileView/utlis'
export default {
  name: 'Points',
  data() {
    return {
      staffId: getstaffId(), // localStorage.getItem('staffId'), // 获取当前登录用户的id
      activeName: 'first', // 默认选中“积分任务”
      tasks: [

      ],
      details: [

      ],
      page: {
        pageNum: 0,
        pageSize: 20
      },
      loading: false, // 加载状态
      noMoreData: false // 是否没有更多数据

    }
  },
  mounted() {
    this.getIntegralTaskList()
    this.getIntegralDetails()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll, true)
  },
  methods: {
    handleClick(name) {
      this.activeName = name
      if (name === 'second') {
        window.addEventListener('scroll', this.handleScroll, true)
        this.page.pageNum = 0
        this.details = []
        this.noMoreData = false
        this.getIntegralDetails()
      }
      if (name === 'first') {
        window.removeEventListener('scroll', this.handleScroll, true)
        this.page.pageNum = 0
        this.tasks = []
        this.noMoreData = false
        this.getIntegralTaskList()
      }
    },
    completeTask(task) {
      // 处理任务完成逻辑，例如更新任务状态
      this.$router.push({ path: '/mobile' })
    },
    async getIntegralTaskList() {
      const res = await tasklist({ staffId: this.staffId })
      if (res.code !== 200) {
        this.$message.warning({ message: res.message, duration: 3000 })
        return
      }
      this.tasks = res.data
    },
    async getIntegralDetails() {
      if (this.noMoreData) {
        window.removeEventListener('scroll', this.handleScroll, true)
        return
      }
      if (this.loading) return
      this.loading = true
      this.page.pageNum++
      try {
        const res = await pointsdetails({ staffId: this.staffId, page: this.page, filter: {}})
        if (res.code !== 200) {
          this.$message.warning({ message: res.message, duration: 3000 })
          this.loading = false
          return
        }
        const newDetails = res.data.dataList
        if (newDetails.length < this.page.pageSize) {
          this.noMoreData = true
        }
        this.details = [...this.details, ...newDetails]
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    handleScroll() {
      if (this.activeName === 'second') {
        const scrollTop = this.$refs.details.scrollTop
        const windowHeight = this.$refs.details.clientHeight
        const scrollHeight = this.$refs.details.scrollHeight
        // console.log('scrollTop',scrollTop)
        // console.log('windowHeight',windowHeight)
        // console.log('scrollHeight',scrollHeight)
        if (scrollTop + windowHeight >= scrollHeight - 10) {
          console.log('符合条件')
          this.getIntegralDetails()
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.getPoints{
  height: 100%;
  overflow: auto;
}
.tabs {
  display: flex;
  justify-content: center; // 水平居中
  border-bottom: 1px solid #e4e7ed; // 底部边框
  padding: 0 90px; // 内边距
}

.tab-item {
  padding: 16px 24px; // 内边距
  font-size: 16px;
  color: #303133;
  cursor: pointer;
  position: relative;
  margin: 0 16px;
  white-space: nowrap;

  &.active {
    color: #67C23A; // 选中时的文字颜色

    &::after {
      content: '';
      position: absolute;
      bottom: -1px; // 紧贴底部边框
      left: 50%;
      transform: translateX(-50%);
      width: 60px; // 绿色线条的宽度
      height: 2px; // 绿色线条的高度
      background-color: #67C23A; // 绿色线条的颜色
    }
  }
}

.tasks {
  padding: 20px;
  height: calc(100vh - 53px);
  overflow: auto;
  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.task-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 10px 0;

  .task-title {
    font-size: 16px;
    margin-bottom: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .task-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
  }

  .task-action {
    text-align: right;
  }
}
.tags{
  width: 70px;
  text-align: center;
}

.details {
  padding: 20px;
  height: calc(100vh - 53px);
  overflow: auto;
  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  .titlt{
    padding: 10px 0;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    .detail-points{
      flex: 1;
    }
  }
  .detail-item {
    padding: 14px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    .detail-name {
      font-size: 14px;
      color: #333;
      width: 45%;
    }

    .detail-points {
      font-size: 14px;
      color: #666;
      margin: 0 10px;
      width: 15%;
    }

    .detail-time {
      font-size: 12px;
      color: #999;
      width: 35%;
    }
  }
   .loading, .no-more-data {
    text-align: center;
    padding: 20px 0;
    font-size: 14px;
    color: #999;
  }
}
</style>
