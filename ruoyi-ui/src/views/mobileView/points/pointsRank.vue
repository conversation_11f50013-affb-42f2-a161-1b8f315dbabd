<template>
  <div class="PointsRank">
    <div class="tabs">
      <div
        class="tab-item"
        :class="{ active: activeName === 'today' }"
        @click="handleClick('today')"
      >
        今日
      </div>
      <div
        class="tab-item"
        :class="{ active: activeName === 'week' }"
        @click="handleClick('week')"
      >
        本周
      </div>
      <div
        class="tab-item"
        :class="{ active: activeName === 'month' }"
        @click="handleClick('month')"
      >
        本月
      </div>
    </div>

    <!-- 积分排名内容 -->
    <div v-loading="loading" class="rank-list">
      <!-- 标题 -->
      <div class="rank-title">
        <div class="rank">排名</div>
        <div class="orgName">单位</div>
        <div class="name">用户名</div>
        <div class="points">积分</div>
      </div>
      <div v-for="(item, index) in rankData" :key="index" class="rank-item">
        <div class="ranking">
          <svg-icon v-if="index < 3" :icon-class="'no' + index" />
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div class="orgName">{{ item.orgName? item.orgName : '--' }}</div>
        <div class="name">{{ item.userName? item.userName : '--' }}</div>
        <div class="points">{{ item.points? item.points : '--' }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRankingListRequest } from '@/api/mobile'
import { getstaffId } from '@/views/mobileView/utlis'
export default {
  name: 'PointsRank',
  data() {
    return {
      staffId: getstaffId(),
      activeName: 'today', // 默认选中“昨日”
      loading: false,
      rankData: [

      ]
    }
  },
  mounted() {
    this.getList('today')
  },
  methods: {
    handleClick(name) {
      this.activeName = name
      this.getList(name)
    },
    async getList(time) {
      this.loading = true
      const res = await getRankingListRequest(time, { staffId: this.staffId })
      if (res.code !== 200) return this.$message.warning({ message: res.message, duration: 3000 })
      this.rankData = res.data
      this.loading = false
      console.log(res)
    }

  }
}
</script>

<style scoped lang="scss">
.PointsRank {
  height: 100%;
  overflow: auto;

  .tabs {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #e4e7ed;
    padding: 0 90px;

    .tab-item {
      padding: 16px 24px;
      font-size: 16px;
      color: #303133;
      cursor: pointer;
      position: relative;
      margin: 0 16px;
      white-space: nowrap;

      &.active {
        color: #67C23A;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 50%;
          transform: translateX(-50%);
          width: 38px;
          height: 2px;
          background-color: #67C23A;
        }
      }
    }
  }

  .rank-list {
    // padding: 20px 60px;
    padding: 20px 20px;
    height: calc(100vh - 53px);
    overflow: auto;
    .rank-title{
      display: flex;
      // align-items: center;
      // justify-content: space-between;
      margin-top: 20px;
      margin-bottom: 20px;
      font-weight: bold;
      text-align: center;
      .rank{
        flex: 1;
      }
      .orgName{
        flex:3
      }
      .name{
        flex:2
      }
      .points{
        flex:1
      }
    }

    .rank-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;
      // border-bottom: 1px solid #f0f0f0;
      font-size: 14px;
      text-align: center;
      .ranking {
        flex: 1;
        justify-content: center;
        // text-align: center;
        // width: 30px;
        svg {
          font-size: 20px;
        }

        span {
          color: #666;
        }
      }
      .orgName{
        flex: 3;
        justify-content: center;
        color: #333;
        // width: 150px;
        // text-align: center;
      }
      .name {
        flex: 2;
        justify-content: center;
        color: #333;
        // width: 100px;
        // text-align: center;
      }

      .points {
        flex: 1;
        justify-content: center;
        // width: 30px;
        color: #333;
        // text-align: left;
      }
    }
  }
}

</style>
