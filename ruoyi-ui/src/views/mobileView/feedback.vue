<template>
  <div class="mobilefeedback-container">
    <!-- 反馈内容输入框 -->

    <el-form ref="feedbackForm" :model="form">
      <div class="labelText">反馈内容:</div>
      <el-form-item label="">
        <el-input
          v-model="form.feedbackContent"
          type="textarea"
          placeholder="请您详细描述您的反馈内容"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <div class="labelText">
        上传图片
        <span>（最多可上传5张）：</span>
      </div>
      <!-- 上传图片 -->
      <el-form-item>
        <div class="image-list">
          <div v-for="(item, index) in images" :key="index" class="image-item">
            <div class="image-wrapper">
              <img :src="item.url" alt="上传图片" class="preview-image" />
              <!-- 添加上传状态指示器 -->
              <div v-if="item.status === 'uploading'" class="image-overlay uploading">
                <div class="loading-spinner"></div>
                <!-- <div class="loading-text">上传中...</div> -->
              </div>
              <div v-else-if="item.status === 'success'" class="success">
                <div class="image-overlay" @click="showImageActions(index)">
                  <div class="image-actions">
                    <i class="el-icon-delete" @click.stop="removeImage(index)" />
                  </div>
                </div>
              </div>
              <div v-else-if="item.status === 'error'" class="image-overlay error">
                <i class="el-icon-warning"></i>
              </div>

              <!-- 始终显示操作按钮，但根据状态控制显示 -->
            </div>
          </div>
          <div v-if="images.length < 5" class="upload-button" @click="triggerUpload">
            <i class="el-icon-plus" />
          </div>
        </div>
        <input
          ref="fileInput"
          type="file"
          accept=".jpg,.png,.gif,.jpeg"
          multiple
          style="display: none"
          @change="handleFileChange"
        />
      </el-form-item>

      <!-- 提交按钮 -->
      <el-form-item>
        <el-button
          class="submit-btn"
          type="primary"
          style="width: 90%"
          :loading="isSubmitting"
          :disabled="uploading"
          @click="submitFeedback"
          >{{ isSubmitting ? '提交中...' : uploading ? '图片上传中...' : '提交' }}</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="previewVisible" append-to-body class="preview-dialog">
      <img :src="previewUrl" alt="预览图片" class="preview-image-large" />
    </el-dialog>
  </div>
</template>

<script>
import CryptoJS from 'crypto-js'
import { uploadImgRequest, feedbackRequest } from '@/api/mobile'
import { getstaffId } from '@/views/mobileView/utlis'
import { eventBus } from '@/utils/eventBus'
import { debounce } from '@/utils'

export default {
  name: 'Feedback',
  data() {
    return {
      staffId: getstaffId(),
      form: {
        feedbackContent: '' // 反馈内容
      },
      images: [], // 已上传图片列表
      isSubmitting: false, // 防重复提交标志
      uploading: false, // 添加上传状态标志
      previewVisible: false,
      previewUrl: '',
      debouncedSubmitFeedback: null // 防抖函数
    }
  },
  created() {
    // 创建防抖函数，延迟500ms
    this.debouncedSubmitFeedback = debounce(this.submitFeedbackInternal.bind(this), 500)
  },
  methods: {
    // 触发文件选择
    triggerUpload() {
      this.$refs.fileInput.click()
    },

    // 处理文件选择事件
    async handleFileChange(e) {
      const files = e.target.files
      if (!files || files.length === 0) return

      // 检查是否超出限制
      if (this.images.length + files.length > 5) {
        this.$message.warning({ message: `最多只能上传 5 张图片！`, duration: 3000 })
        return
      }

      // 设置上传状态为true
      this.uploading = true

      try {
        // 使用for循环顺序处理文件
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          // 限制图片大小为10M
          if (file.size > 10 * 1024 * 1024) {
            this.$message.closeAll()
            this.$message.warning({ message: `${file.name} 超过10M，无法上传！`, duration: 3000 })
            continue
          }

          // 使用Promise包装FileReader
          const fileData = await new Promise((resolve) => {
            const reader = new FileReader()
            reader.onload = (e) => {
              resolve(e.target.result)
            }
            reader.readAsDataURL(file)
          })

          // 生成唯一标识符：时间戳 + 随机数 + 索引
          const uniqueId = Date.now() + '_' + Math.random().toString(36).substring(2, 10) + '_' + i

          // 计算文件的MD5，加入唯一标识符确保即使相同文件也有不同的MD5
          const md5 = CryptoJS.MD5(fileData + uniqueId).toString()

          // 添加到文件列表，初始状态为uploading
          const fileIndex = this.images.length
          this.images.push({
            url: fileData,
            name: file.name,
            md5: md5,
            status: 'uploading' // 添加状态字段
          })

          // 创建一个新的FormData对象
          const formData = new FormData()
          formData.append('file', file)

          // 使用MD5作为上传参数
          await this.uploadAvatar(md5, formData, fileIndex)
        }
      } finally {
        // 无论上传成功或失败，都重置上传状态
        this.uploading = false
      }

      // 清空文件输入框，允许重复选择相同的文件
      e.target.value = ''
    },

    // 实际上传方法
    async uploadAvatar(md5, formData, fileIndex) {
      try {
        const currentFile = this.images[fileIndex]

        // 发送上传请求，使用MD5作为请求参数
        const response = await uploadImgRequest(md5, formData)

        if (response && response.code === 200) {
          this.$message.success({ message: `${currentFile.name} 上传成功`, duration: 3000 })

          // 更新文件的ID和状态
          this.images[fileIndex].id = response.data.id
          this.images[fileIndex].status = 'success'
        } else {
          this.$message.warning({ message: `${currentFile.name} 上传失败`, duration: 3000 })
          this.images[fileIndex].status = 'error'
        }

        return response
      } catch (error) {
        console.error(`文件上传异常:`, error)
        this.images[fileIndex].status = 'error'
        this.$message.warning({ message: `文件上传失败`, duration: 3000 })
        throw error
      }
    },

    // 图片预览方法
    previewImage(item) {
      this.previewUrl = item.url
      this.previewVisible = true
    },

    // 下载图片方法
    downloadImage(item) {
      try {
        // 创建一个临时的a标签用于下载
        const link = document.createElement('a')
        link.href = item.url
        link.download = item.name || '图片.png' // 使用原始文件名或默认名称
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error('下载图片失败:', error)
        this.$message.warning({ message: '图片下载失败', duration: 3000 })
      }
    },

    // 删除图片方法
    removeImage(index) {
      // 如果正在上传，不允许删除图片
      if (this.uploading) {
        this.$message.warning({ message: '图片上传中，请稍候...', duration: 3000 })
        return
      }

      // 检查要删除的图片是否正在上传
      if (this.images[index].status === 'uploading') {
        this.$message.warning({ message: '图片上传中，无法删除', duration: 3000 })
        return
      }

      this.images.splice(index, 1)
      this.$message.success({ message: '图片已删除', duration: 3000 })
    },

    showImageActions(index) {
      // 可以在这里添加点击图片显示操作按钮的逻辑
    },

    // 文件超出限制时的处理
    handleExceed(files, fileList) {
      this.$message.warning({ message: '最多只能上传 5 张图片！', duration: 3000 })
    },

    // 提交反馈
    async submitFeedback() {
      // 防重复提交检查
      if (this.isSubmitting) {
        console.log('正在提交中，请稍候...')
        return
      }

      // 如果正在上传，不允许提交
      if (this.uploading) {
        this.$message.warning({ message: '图片上传中，请稍候...', duration: 3000 })
        return
      }

      // 检查是否有上传失败的图片
      const hasError = this.images.some((file) => file.status === 'error')
      if (hasError) {
        this.$message.warning({ message: '存在上传失败的图片，请删除后重新上传', duration: 3000 })
        return
      }

      // 使用防抖函数处理提交
      this.debouncedSubmitFeedback()
    },

    // 实际的提交反馈逻辑
    async submitFeedbackInternal() {
      // 设置提交状态
      this.isSubmitting = true

      try {
        if (this.form.feedbackContent === '') {
          this.$message.closeAll()
          this.$message.warning({ message: '请填写反馈内容！', duration: 3000 })
          return
        }
        const data = {
          userCode: this.staffId,
          feedbackContent: this.form.feedbackContent,
          imageIds: this.images
            .filter((img) => img.status === 'success') // 只提交上传成功的图片
            .map((img) => img.id)
            .join(','),
          visitFrom: localStorage.getItem('visitFrom')
        }
        const res = await feedbackRequest(data)
        if (res.code !== 200) {
          this.$message.error({ message: res.msg, duration: 3000 })
          return
        }
        this.$message.success({ message: '提交成功！', duration: 3000 })
        // 触发积分刷新事件
        eventBus.$emit('refreshPoints')
        // 返回上一级页面
        this.$router.go(-1)

        // 清空表单
        this.form.feedbackContent = ''
        this.images = []
      } catch (error) {
        console.error('提交反馈失败:', error)
        this.$message.error({ message: '提交失败，请稍后重试', duration: 3000 })
      } finally {
        // 重置提交状态
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.mobilefeedback-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100vw;
  height: 100vh;
  padding: 16px;
  box-sizing: border-box;

  .el-form {
    width: 100%;
  }
  .labelText {
    font-size: 16px;
    margin-bottom: 12px;
    span {
      font-size: 12px;
      color: #c0c0c0;
    }
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .image-item {
    position: relative;
    width: 64px;
    height: 64px;
    border-radius: 4px;
    overflow: hidden;
  }

  .image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none; /* 默认不拦截点击事件 */

    &.uploading {
      background-color: rgba(0, 0, 0, 0.7);
      opacity: 1;
      flex-direction: column;
      color: white;
      pointer-events: auto; /* 上传中时拦截点击事件 */
    }

    &.success {
      background-color: rgba(103, 194, 58, 0.8);
      opacity: 1;
      color: white;
      font-size: 24px;
    }

    &.error {
      background-color: rgba(245, 108, 108, 0.8);
      opacity: 1;
      color: white;
      font-size: 24px;
    }

    &:not(.uploading):not(.success):not(.error) {
      opacity: 1;
      background-color: rgba(0, 0, 0, 0.5);
      pointer-events: auto; /* 允许点击事件 */
    }
  }

  .image-actions {
    display: flex;
    gap: 10px;

    i {
      color: white;
      font-size: 16px;
      cursor: pointer;

      &:hover {
        color: #67c23a;
      }
    }

    &.permanent {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;

      i {
        color: white;
        font-size: 16px;
        cursor: pointer;
        pointer-events: auto; /* 确保图标可以点击 */
      }

      &:hover {
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  // 添加加载动画
  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin-bottom: 0px;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    font-size: 10px;
    color: white;
  }

  .upload-button {
    width: 64px;
    height: 64px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    i {
      font-size: 20px;
      color: #8c939d;
    }

    &:hover {
      border-color: #67c23a;

      i {
        color: #67c23a;
      }
    }
  }

  .submit-btn {
    position: fixed;
    bottom: 30px;
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
  }
  ::v-deep .el-button--primary {
    color: #4cb848;
    background-color: #ebf5e8;
    border: none;
  }

  .preview-dialog {
    ::v-deep .el-dialog {
      margin: 0 !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 90%;
      max-height: 90%;
    }

    ::v-deep .el-dialog__body {
      padding: 10px;
      text-align: center;
    }
  }

  .preview-image-large {
    max-width: 100%;
    max-height: 80vh;
  }
}
</style>
