<template>
  <div class="excelList">
    <div class="content">
      <!-- <div class="title">
        <svg-icon icon-class="excelAssistant"></svg-icon>
        Excel助手
      </div> -->
      <div v-show="excelBox" class="list">
        <div class="item">
          <div class="head">
            <span
              style="font-size: 14px; color: #262626"
            >快速进行数据合并/拆分、去重等复杂操作。</span>
          </div>
          <div class="footer" @click="handleClick(0)">立即体验</div>
        </div>
        <div class="item">
          <div class="head">
            <span
              style="font-size: 14px; color: #262626"
            >精准进行数据对比分析、统计分析、透视分析、相关分析。</span>
          </div>
          <div class="footer" @click="handleClick(1)">立即体验</div>
        </div>
        <!-- <div class="item">
          <div class="head">
            <span
              style="font-size: 14px; color: #262626"
            >绘制柱状图、饼状图、折线图、流程图等各种可视化图表。</span>
          </div>
          <div class="footer" @click="handleClick(2)">立即体验</div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ExcelList',
  props: {
    excelBox: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API
    }
  },
  methods: {
    handleClick(index) {
      this.$emit('handleExcelClick', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.excelList {
  // padding: 15px 25px 10px 25px;
  // min-height: 180px;
  width: 100%;
}
.content {
  // border-radius: 30px;
  // border-top-left-radius: 30px;
  // border-top-right-radius: 30px;
  // padding: 15px;
  // padding-bottom: 10px;
  .title {
    color: #000;
  }
}
.list {
  display: flex;
  justify-content: space-between;
  margin-top: 14px;
  padding: 6px 25px 6px 25px;
  // border-bottom: 1px solid #727272;
  .item {
    padding: 10px;
    border-radius: 10px;
    background-color: #fff;
    border: 1px solid #4cb848;
    width: 49%;

    .head {
      // display: flex;
      // align-items: center;
      height: 60px;
    }
    .footer {
      width: 100%;
      color: #4cb848;
      text-align: center;
      margin-top: 10px;
      font-size: 14px;
    }
  }
}
</style>
