class FileMsgClass {
  constructor(dataId, obj, content, fileList, historyList) {
    const
      imageFileExtensions = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'bmp',
        'svg',
        'webp',
        'tiff',
        'tif',
        'psd',
        'raw',
        'heic',
        'heif',
        'avif',
        'ico',
        'cur',
        'apng',
        'pjpeg',
        'pjp'
      ]
    this.dataId = dataId
    this.hideInUI = false
    this.obj = obj
    this.status = ''
    this.time = new Date().toISOString() // ISO 8601格式
    this.value = []
    if (content) {
      this.value.push({
        type: 'text',
        text: {
          content: content
        }
      })
    }
    if (fileList) {
      fileList.map(item => {
        let type = 'file'
        if (imageFileExtensions.includes(item.raw.type.split('/')[1])) {
          type = 'image'
        }
        this.value.push({
          'type': 'file',
          'file': {
            'name': item.name,
            'type': type,
            'url': item.response.data.previewUrl
          }
        })
      })
    }
    if (historyList) {
      historyList.map(item => {
        if (item.type === 'text') {
          this.value.push({
            type: 'text',
            text: {
              content: item.text.content
            }
          })
        } else if (item.type === 'file') {
          let type = 'file'
          if (item.file.type === 'image') {
            type = 'image'
          }
          this.value.push({
            'type': 'file',
            'file': {
              'name': item.file.name,
              'type': type,
              'url': item.file.url
            }
          })
        }
      })
    }
  }

  // 获取完整消息对象
  getMessage() {
    return {
      dataId: this.dataId,
      hideInUI: this.hideInUI,
      obj: this.obj,
      value: this.value,
      time: this.time
    }
  }

  // 更新消息内容
  updateContent(newContent) {
    this.value[0].text.content = newContent
    this.time = new Date().toISOString()
  }

  // 获取当前内容
  getContent() {
    return this.value[0].text.content
  }

  // 获取格式化时间
  getFormattedTime() {
    return new Date(this.time).toLocaleString()
  }
}

export default FileMsgClass
