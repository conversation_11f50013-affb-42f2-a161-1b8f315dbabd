import { generateChatId } from '@/utils/deepseek'

class TextCompletions {
  constructor(dataId, content, appId, chatId, shareId, outLinkUid, translateFrom, translateTo) {
    this.messages = [
      {
        dataId: dataId,
        hideInUI: false,
        role: 'user',
        content: content
      }
    ]

    this.variables = {
      cTime: this._getCurrentTime()
    }

    // 添加翻译相关参数
    // if (translateFrom) {
    //   this.variables.translateFrom = translateFrom
    // }
    // if (translateTo) {
    //   this.variables.translateTo = translateTo
    // }

    this.responseChatItemId = generateChatId(24)
    // this.appId = appId
    this.chatId = chatId
    this.shareId = shareId
    this.outLinkUid = outLinkUid
    this.detail = true
    this.stream = true
    // this.translateFrom = translateFrom
    // this.translateTo = translateTo
  }

  // 获取格式化的当前时间
  _getCurrentTime() {
    const date = new Date()

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const dayName = days[date.getDay()]

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${dayName}`
  }

  // 可选：获取完整请求对象
  getRequestObject() {
    return {
      messages: this.messages,
      variables: this.variables,
      responseChatItemId: this.responseChatItemId,
      appId: this.appId,
      chatId: this.chatId,
      detail: this.detail,
      stream: this.stream
    }
  }
}

export default TextCompletions

// 使用示例
// const completion = new TextCompletions(
//   "snwKP8pyQcBqSbOene2hVcDv",
//   "我测试一下提问接口",
//   "67889533a13c9cb2f3b2a708",
//   "faPQ8E8BpG
