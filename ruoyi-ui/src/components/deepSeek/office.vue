<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="60%"
    top="0vh"
    :fullscreen="true"
    class="office-dialog"
    :before-close="handleClose"
  >
    <vue-office-pdf
      v-if="type === 'pdf'"
      :src="url"
      class="office"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <vue-office-excel
      v-else-if="type === 'xlsx'"
      :src="url"
      class="office"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <vue-office-docx
      v-else-if="type === 'docx'"
      :src="url"
      class="office"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <vue-office-pptx
      v-else-if="type === 'pptx'"
      :src="url"
      class="office"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <iframe v-else :src="url" width="100%" frameborder="0" class="office" />
  </el-dialog>
</template>
<script>
// 引入VueOfficeDocx组件
// import VueOfficeDocx from '@vue-office/docx'
// 引入相关样式
import '@vue-office/docx/lib/index.css'
// 引入VueOfficeExcel组件
// import VueOfficeExcel from '@vue-office/excel'
// 引入相关样式
import '@vue-office/excel/lib/index.css'
// import VueOfficePdf from '@vue-office/pdf'
// import VueOfficePptx from '@vue-office/pptx'
export default {
  name: 'Office',
  components: {
    // 改为动态导入
    VueOfficeDocx: () =>
      import(/* webpackChunkName: "vue-office-docx" */ '@vue-office/docx'),
    VueOfficeExcel: () =>
      import(/* webpackChunkName: "vue-office-excel" */ '@vue-office/excel'),
    VueOfficePdf: () =>
      import(/* webpackChunkName: "vue-office-pdf" */ '@vue-office/pdf'),
    VueOfficePptx: () =>
      import(/* webpackChunkName: "vue-office-pptx" */ '@vue-office/pptx')
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    url: {
      type: [String, Blob],
      default: ''
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      type: 'docx'
    }
  },
  created() {
    this.dialogVisible = true
    const fileType = this.name.split('.').pop().toLowerCase()
    if (fileType === 'pdf') {
      this.type = 'pdf'
    } else if (fileType === 'xlsx') {
      this.type = 'xlsx'
    } else if (fileType === 'docx') {
      this.type = 'docx'
    } else if (fileType === 'pptx') {
      this.type = 'pptx'
    } else {
      this.type = 'txt'
    }
  },
  methods: {
    renderedHandler() {
      console.log('渲染完成')
    },
    errorHandler() {
      console.log('渲染失败')
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('update:value', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.office {
  height: 90vh;
  ::v-deep .pptx-preview-wrapper,
    ::v-deep .vue-office-pdf-wrapper,
    ::v-deep .docx-wrapper
    // ::v-deep .x-spreadsheet
  {
    background: rgba(238, 238, 238, 1) !important;
  }
}

::v-deep.office-dialog .el-dialog:not(.is-fullscreen) {
  margin-top: 0vh !important;
}
::v-deep.office-dialog .el-dialog__headerbtn .el-dialog__close {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
}
</style>
