<template>
  <div
    class="comMsgRight text-right"
    @mouseenter="handleMsgInnerMouseEnter"
    @mouseleave="handleMsgInnerMouseLeave"
  >
    <div class="flex text-right buttonEa">
      <!-- <el-button-group>
        <el-button
          v-clipboard="copyText"
          v-clipboard:success="clipboardSuccessHandle"
          @click="copyClick"
          class="padding-8"
          size="mini"
          type=""
          plain
        >
          <svg-icon icon-class="copy" />
        </el-button>
        <el-button
          class="padding-8 hoverGreen"
          size="mini"
          type=""
          icon="el-icon-refresh"
          @click="reSubmit"
        ></el-button>
        <el-button
          class="padding-8 hoverRed"
          size="mini"
          type=""
          icon="el-icon-delete"
          @click="delClick"
        ></el-button>
      </el-button-group> -->
      <div
        slot="reference"
        class="text-left radius msgEa send-msg-container"
        :style="{ width: remobileBox ? '85%' : '100%' }"
      >
        <LongPressPopup ref="longPressPopup">
          <!-- 被长按的目标元素 -->
          <template slot="target">
            <div class="msgInner">
              <div
                v-if="fileList && fileList.length"
                :class="remobileBox ? '' : 'flex'"
                style="align-items: flex-start; flex-wrap: wrap; width: 100%"
              >
                <div
                  v-for="(v, i) of fileList"
                  :key="i"
                  :class="{
                    'msg-img-container': v.file.type === 'image',
                    'msg-file-container': v.file.type === 'file'
                  }"
                >
                  <el-image
                    v-if="v.file.type == 'image'"
                    style="cursor: pointer"
                    class="msg imgEa"
                    :preview-src-list="[v.file.image]"
                    :src="v.file.image"
                  >
                    <div slot="error" class="image-error">
                      <!-- <svg-icon icon-class="imgError" /> -->
                      <img src="@/assets/images/img-error.png" alt="" />
                    </div>
                  </el-image>
                  <file-preview
                    v-if="v.file.type == 'file'"
                    :class="
                      remobileBox
                        ? 'remobileBox margin-bottom-10'
                        : 'margin-right-10 margin-bottom-10'
                    "
                    :progress="false"
                    :file="v.file"
                    @click.native="fileClick(v)"
                  />
                </div>
              </div>
              <div class="no-select-text" style="text-align: left">
                <div v-text="sanitizedContent" />
                <!-- {{ sanitizedContent }} -->
              </div>
            </div>
          </template>
          <!-- 弹窗内容 -->
          <template slot="content">
            <div class="popup-content">
              <div
                class="popup-item"
                v-clipboard="copyText"
                v-clipboard:success="clipboardSuccessHandle"
                @click="handleOptionClick('copy')"
              >
                <svg-icon icon-class="copy1" class="msg-icon" /> 复制
              </div>
              <div class="popup-item" @click="handleOptionClick('edit')">
                <svg-icon icon-class="edit1" class="msg-icon" /> 修改
              </div>
              <div class="popup-item" @click="handleOptionClick('delete')">
                <svg-icon icon-class="delete1" class="msg-icon" /> 删除
              </div>
            </div>
          </template>
        </LongPressPopup>
      </div>
      <!-- 右侧的头像 -->
      <!-- <div
        class="padding-2 margin-left-5"
      >
        <svg-icon icon-class="zd1" style="font-size: 32px" />
      </div> -->
    </div>
    <div class="send-msg-container-focus" v-show="btnsFlag">
      <div class="send-msg-btns">
        <el-tooltip effect="dark" :disabled="disabledTrigger" content="复制">
          <a class="focus-btn">
            <div
              v-clipboard="copyText"
              v-clipboard:success="clipboardSuccessHandle"
              class="focus-btn"
              style="display: inline-block"
            >
              <svg-icon icon-class="copy1" class="msg-icon" />
            </div>
          </a>
        </el-tooltip>
      </div>
      <div class="send-msg-btns">
        <el-tooltip effect="dark" :disabled="disabledTrigger" content="编辑">
          <a class="focus-btn" style="display: inline-block" @click="handleOptionClick('edit')">
            <svg-icon icon-class="edit1" class="msg-icon" />
          </a>
        </el-tooltip>
      </div>
      <div class="send-msg-btns">
        <el-tooltip effect="dark" :disabled="disabledTrigger" content="删除">
          <a @click="delClick">
            <svg-icon icon-class="delete1" class="msg-icon" />
          </a>
        </el-tooltip>
      </div>
    </div>
    <!--    markdown-->

    <!-- 修改按钮组结构 - 右对齐 -->
    <!-- <div class="right-aligned-action-buttons">
      <div class="action-btn" v-clipboard="copyText" v-clipboard:success="clipboardSuccessHandle">
        <svg-icon icon-class="copy1" />
        <span>复制</span>
      </div>
      <div class="action-btn" @click="reSubmit">
        <svg-icon icon-class="refresh-sent" />
        <span>重新发送</span>
      </div>
      <div class="action-btn" @click="delClick">
        <svg-icon icon-class="delete1" />
        <span>删除</span>
      </div>
    </div> -->
    <!-- office弹窗 -->
    <vue-office v-if="showOffice" :value.sync="showOffice" :url="showUrl" :name="fileName" />
  </div>
</template>
<script>
import { itemDelete, getFileSize, getOfficeSize } from '@/api/deepSeekApi'
import vueOffice from './office.vue'
import LongPressPopup from './LongPressPopup.vue'

export default {
  name: 'ComMsgRight',
  components: {
    vueOffice,
    LongPressPopup
  },
  props: {
    info: { type: [String, Object] },
    appId: { type: [String] },
    chatId: { type: [String] }
  },
  data() {
    return {
      remobileBox: false,
      isHtml: false,
      showOffice: false,
      fileName: '',
      showUrl: '',
      msg: {
        value: [
          {
            type: 'text',
            text: {
              content: ''
            }
          }
        ]
      },
      copyText: '',
      fileList: [],
      deviceType: localStorage.getItem('deviceType'),
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile',
      btnsFlag: false
    }
  },
  computed: {
    sanitizedContent() {
      return this.copyText.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
    }
  },
  watch: {
    info: {
      handler(newValue, oldValue) {
        // console.log('info', newValue)
        this.fileList = []
        if (newValue && newValue.obj.toLowerCase() === 'human') {
          if (newValue.value.length > 0) {
            newValue.value.forEach(async (element) => {
              // console.log('element', element)
              if (element.type === 'text') {
                this.copyText = element.text.content
              } else if (element.type === 'file') {
                const fileType = element.file.type
                if (fileType === 'image') {
                  element.file.type = fileType
                  const res = await getFileSize({
                    fileUrl: element.file.url
                  })
                  if (res) {
                    const type = res.type
                    const blob = new Blob([res], { type: type })
                    const url = URL.createObjectURL(blob)
                    element.file.image = url
                  }
                }
                this.fileList.push(element)
              } else {
                console.log('type not support', element)
              }
            })
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    if (this.info && this.info.obj === 'Human') {
      // this.rawMarkdown = this.info.value[0].text.content
    }
  },
  mounted() {
    // 判断是不是手机端
    this.remobileBox = localStorage.getItem('deviceType') === 'mobile'
  },
  methods: {
    copyClick() {
      // this.writeToClipboard(this.info.value[0].text.content)
    },
    clipboardSuccessHandle() {
      // this.$message.success('复制成功')
      this.$message.closeAll()
      this.$message.success({ message: '复制成功', duration: 3000 })
    },
    async writeToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('内容已复制')
      } catch (err) {
        console.error('无法写入剪切板:', err)
        // this.$message.error('无法写入剪切板')
        this.$message.warning('无法写入剪切板')
      }
    },
    reSubmit() {
      this.$emit('sendComMsgRightReSubmit', this.info)
    },
    async fileClick(row) {
      console.log('row', row, this.info)
      const now = Date.now()
      const old = new Date(this.info.time).getTime()
      const outDate = now - old > 30 * 24 * 60 * 60 * 1000
      if (outDate) {
        // this.$message.error('文件已过期，请重新发送')
        this.$message.closeAll()
        this.$message.warning({ message: '文件已过期，请重新发送', duration: 3000 })
        return
      }
      if (this.disabledTrigger) {
        const res = await getOfficeSize({
          fileUrl: row.file.url
        })
        console.log(res, 'res')
        if (res && res.code === 200) {
          if (window.wx && window.wx.previewFile) {
            const url = window.location.origin
            window.wx.previewFile({
              url: `${url}${res.data.fileUrl}`,
              name: row.file.name,
              size: res.data.contentLength
            })
          }
        } else {
          // this.$message.error('文件预览失败')
          this.$message.warning('文件预览失败')
          return
        }
      } else {
        const fileType = row.file.name.split('.').pop().toLowerCase()
        const res = await getFileSize({
          fileUrl: row.file.url
        })
        if (res) {
          console.log('res', res)
          const type = res.type
          const blob = new Blob([res], { type: type })
          const url = URL.createObjectURL(blob)
          if (['doc', 'ppt', 'xls'].includes(fileType)) {
            // this.$message.error('暂不支持该文件类型预览')
            this.$message.warning('暂不支持该文件类型预览')
            return
          }
          this.showOffice = true
          this.showUrl = url
          this.fileName = row.file.name
        } else {
          // this.$message.error('文件预览失败')
          this.$message.warning('文件预览失败')
          return
        }
      }

      // if (res) {
      //   console.log('res', res)
      //   const type = res.type
      //   const blob = new Blob([res], { type: type })
      //   const url = URL.createObjectURL(blob)
      //   if (this.disabledTrigger) {
      //     if (wx && wx.previewFile) {
      //       wx.previewFile({
      //         url: url,
      //         name: row.file.name,
      //         size: res.size
      //       })
      //     }
      //   } else {
      //     if (['doc', 'ppt', 'xls'].includes(fileType)) {
      //       this.$message.error('暂不支持该文件类型预览')
      //       return
      //     }
      //     this.showOffice = true
      //     this.showUrl = url
      //     this.fileName = row.file.name
      //   }
      // }
    },
    delClick(row) {
      console.log(row)
      // this.$confirm('是否删除该消息?', '提示', {
      this.$alert(
        `<span class="custom-title">提示</span>
             <div class="alert-content">
             是否删除该消息?
            </div>`,
        '',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          // type: 'warning',
          // customClass: 'custom-confirm' // 设置自定义类名
          customClass: this.disabledTrigger
            ? 'ismobile-message-box my-custom-alert'
            : 'my-custom-alert',
          dangerouslyUseHTMLString: true
        }
      )
        .then(() => {
          this.delMsg(row)
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // })
        })
    },
    delMsg(row) {
      itemDelete({
        appId: this.appId,
        chatId: this.chatId,
        contentId: this.info.dataId
      }).then((res) => {
        if (res.code === 200) {
          this.$message.closeAll()
          this.$message.success({ message: '删除成功', duration: 3000 }) // 只显示1秒
          this.$emit('sendComMsgRightDel', this.info)
        } else {
          // this.$message.error('删除失败')
          this.$message.closeAll()
          this.$message.warning({ message: '删除失败', duration: 3000 })
        }
      })
    },
    handleOptionClick(action) {
      // 处理选项逻辑
      console.log('选择了:', action)
      switch (action) {
        case 'delete':
          this.delClick()
          break
        case 'edit':
          // 把当前消息传递给父组件，放到输入框
          // let text = this.info.value[0].text.content
          console.log(this.info)
          let textinfo = this.info.value.filter((item) => item.type == 'text')
          let text = textinfo[0].text.content
          console.log(text)
          this.$emit('sendComMsgRightEdit', text)

          break
        default:
          break
      }
      // 关闭弹窗（核心代码）
      this.$refs.longPressPopup.closePopup()
    },
    handleMsgInnerMouseEnter() {
      // 这里实现鼠标移入事件的处理逻辑
      console.log('鼠标移入了消息区域')
      if (!this.disabledTrigger) {
        this.btnsFlag = true
      }
    },
    handleMsgInnerMouseLeave() {
      this.btnsFlag = false
    }
  }
}
</script>

<style lang="scss">
// 添加pc端样式 像素超过1000px时 样式生效 小于1000px时加载手机端样式
.custom-confirm {
  width: 80%;
  @media screen and (min-width: 600px) {
    width: 400px;
  }
}
.comMsgRight {
  width: 100%;
  padding: 10px 0 30px 0;
  position: relative;
  .buttonEa {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;

    width: 100%;
    &:hover {
      .time {
        display: block;
      }
    }
  }
  .time {
    font-size: 12px;
    color: #97999b;
    margin-right: 5px;
    display: none;
  }
  .msgEa {
    width: 100%;
    text-align: left;
    overflow: auto;
    .msgInner {
      // text-align: right;
      overflow: hidden;
      border-radius: 12px;
      //max-width: 80%;
      //max-width: 100%;
      float: right;
      background-color: var(--color-primary-light);
      padding: 7px 16px;
      .imgEa {
        img {
          max-width: 300px;
          border-radius: 10px;
          overflow: hidden;
        }
      }
      .msg {
        max-width: 100%;
        overflow: hidden;
        border-radius: 5px 0 5px 5px;
        text-align: left;
      }
      .fileType {
        background-color: white;
        text-align: left;
      }
    }
  }
  .hoverRed {
    &:hover {
      color: #e74694;
    }
  }
  .hoverGreen {
    &:hover {
      color: #039855;
    }
  }
}
</style>
<style lang="scss" scoped>
// 为了让手机端的文件样式跟气泡框宽度相同
.remobileBox {
  // width: 100%;
}
.ismobile-message-box {
  width: 80%;
}

/* 弹窗整体圆角 */
.el-message-box {
  padding: 0;
}
.el-message-box,
.mobile-message-box {
  border-radius: 20px;
  overflow: hidden;
  /* 关键：让内容和圆角贴合 */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
}

/* 按钮区域无圆角，底部整体圆角由外层控制 */
.el-message-box__btns {
  display: flex;
  align-items: stretch;
  overflow: hidden;
  padding: 0;
  border-top: 1px solid #eee;
  height: 48px;
  margin: 0;
  /* 确保无外边距 */
  border-radius: 0 0 20px 20px;
}

/* 公共按钮样式 */
.el-message-box__btns .el-button {
  flex: 1;
  margin: 0;
  min-width: 0;
  /* 关键：去除默认最小宽度 */
  font-size: 14px;
  height: 100%;
  border: none;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  /* 让内容居中 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

/* 取消按钮（第一个） */
.el-message-box__btns .el-button:first-child {
  color: #333;
  border-bottom-left-radius: 20px;
  border-right: 1px solid #eee;
}

/* 已了解按钮（第二个） */
.el-message-box__btns .el-button:last-child {
  color: var(--color-primary);
  border-bottom-right-radius: 20px;
}

.custom-title {
  display: block;
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  color: black;
  margin-top: 20px;
  margin-bottom: 20px;
}
.alert-content {
  line-height: 1.5;
  margin-bottom: 20px;
  padding: 0 15px;
  text-align: left; /* 默认左对齐 */
  max-width: 100%;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

/* 调整消息框内容区域的内边距 */
.el-message-box__content {
  padding: 0;
}

/* 确保消息框标题区域没有内边距 */
.el-message-box__header {
  padding: 0;
}
.el-message-box__headerbtn {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 10px; /* 增加内边距扩大可点击区域 */
  width: 20px; /* 设置基础宽度 */
  height: 20px; /* 设置基础高度 */
  z-index: 10;
  box-sizing: content-box;
}

.msg-img-container {
  width: 100%;
  .msg {
    width: 100%;
  }
}
.send-msg-container-focus {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  position: absolute;
  right: 0px;
  top: 10;

  .send-msg-btns {
    font-size: 18px;
    cursor: pointer;
    font-weight: 400;
    color: #909090;
    padding: 7px 0px;
  }
  .focus-btn:focus {
    color: var(--color-primary);
  }
  .msg-icon {
    margin-right: 6px;
    font-size: 22px;
    color: #909090;
  }
}
.no-select-text {
  user-select: none;
  -webkit-user-select: none;

  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
}

::v-deep .image-error {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}
/* 右对齐的按钮组样式 */
.right-aligned-action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
  width: 100%;
  padding-right: 37px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
  cursor: pointer;
  color: #909090;

  &:hover {
    color: var(--color-primary);
  }

  svg-icon {
    margin-right: 5px;
  }

  span {
    font-size: 12px;
  }
}

.popup-item {
  user-select: none;
  -webkit-user-select: none;
}
</style>
