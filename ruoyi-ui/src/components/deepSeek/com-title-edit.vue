<template>
  <el-dialog class="comTitleEdit" title="组件判定" :visible.sync="showDialog" width="400px" modal-append-to-body>
    <span slot="title" class="dialog-footer">
      <div><span class="el-icon-edit-outline margin-right-10 self-blue" />自定义历史记录标题</div>

    </span>
    <el-form ref="formData" :model="formData" inline label-width="100px">
      <el-form-item label="" prop="">
        <el-input v-model="formData.title" style="width: 350px;" placeholder="如果设置为空，会自动跟随聊天记录" />
      </el-form-item>
    </el-form>
    <el-row class="text-right">
      <el-button @click="showDialog = false">关闭</el-button>
      <el-button type="primary" @click="submit">确认</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import { updateHistory } from '@/api/deepSeekApi'

export default {
  name: 'ComTitleEdit',
  props: {
    info: { type: [String, Object] }
  },
  data() {
    return {
      showDialog: false,
      formData: {
        title: ''
      }
    }
  },
  mounted() {
  },
  methods: {
    show() {
      this.showDialog = true
      this.$nextTick(() => {
        this.init()
      })
    },
    init() {
      console.log('this.info', this.info)
      this.formData.title = this.info.customTitle || this.info.title
    },
    submit() {
      // todo
      updateHistory({ 'appId': this.info.appId, 'chatId': this.info.chatId, 'customTitle': this.formData.title, top: this.info.top }).then(res => {
        if (res.code != 200) {
          // this.$message.error(res.data.msg)
          this.$message.closeAll()
          this.$message.warning({ message: res.data.msg, duration: 3000 })
        } else {
          this.$emit('sendComTitleEdit')
          this.showDialog = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.comTitleEdit{
  .el-dialog__body{
    padding-bottom: 0!important;
  }
}
</style>
