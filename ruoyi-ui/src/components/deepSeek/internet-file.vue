<template>
  <el-drawer
    class="quote-file-container"
    :center="true"
    :visible.sync="showDialog"
    :size="disabledTrigger?'80%':'45%'"
    modal-append-to-body
    :before-close="handleClose"
  >
    <div slot="title" class="quote-title">搜索结果</div>
    <div class="quote-content">
      <div v-for="(item,index) in list" :key="index" class="quote-item">
        <div class="quote-item-title" v-html="item.title" />
        <a :href="item.url" class="quote-item-content" target="_blank" rel="noopener noreferrer">
          <span v-html="item.content" />
        </a>
      </div>
    </div>
  </el-drawer>
</template>
<script>
export default {
  name: 'InternetFile',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDialog: false,
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile'
    }
  },
  created() {
    this.showDialog = this.value
  },
  methods: {
    handleClose() {
      this.showDialog = false
      this.$emit('update:value', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.quote-title{
font-weight: 600;
font-size: 20px;
color: #404040;
}
.quote-content{
  padding:0 16px 20px 16px;


  .quote-item{
    padding: 10px 0;
    .quote-item-content{
      font-size: 14px;
      color:#909399;
      font-weight:400;
      line-height:24px;
    }
  }
}
.quote-item-title{
  font-weight: 600;
font-size: 17px;
color: #404040;
line-height:30px;
}
</style>
