<template>
  <div class="comFileUploadDialog">
    <el-row class="flex">
      <div class="title">{{ funTitle }}</div>
      <el-button style="margin-left: auto" type="text" icon="el-icon-close" @click="closeClick" />
    </el-row>
    <el-upload
      :show-file-list="false"
      style="width: 100%"
      :auto-upload="false"
      :on-change="onChange"
      :on-exceed="handleExceed"
      :before-upload="beforeAvatarUpload"
      class="upload-demo"
      :limit="8"
      drag
      action="https://jsonplaceholder.typicode.com/posts/"
      multiple
    >
      <svg-icon icon-class="upload1" class="svg-icon" />
      <div class="upload-title-dark">在此处拖拽文件</div>

      <div v-if="funTitle === '中英互译'" class="upload-title-light">
        温馨提示：当前支持5000字以内的翻译，请确保文字数量在此范围内。
      </div>
      <div v-else class="upload-title-light">文件大小不超过10M</div>
      <div class="upload-title-light">{{ supportFileText }}</div>
      <div class="upload-btn">从本地选择文件</div>
      <!--      <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>-->
    </el-upload>
  </div>
</template>

<script>
import { Message } from 'element-ui'
import { FILE_TYPE } from '@/utils/constant'
export default {
  name: 'ComFileUploadDialog',
  props: {
    funTitle: {
      default: '上传文件',
      type: String
    }
  },
  data() {
    return {
      showDialog: false,
      timer: null
    }
  },
  computed: {
    supportFileText() {
      // 根据功能类型动态返回支持的文件类型文本
      if (this.funTitle === '中英互译') {
        return '支持：txt、doc、docx、ppt、pptx、pdf、xls、xlsx、jpg、png、jpeg'
      }
      // 其他功能类型可继续扩展
      return '支持：txt、doc、docx、ppt、pptx、pdf、xls、xlsx'
    }
  },
  methods: {
    show() {
      this.showDialog = true
      this.$nextTick(() => {
        this.init()
      })
    },
    init() {},
    beforeAvatarUpload(file, msg = true) {
      let fileType = FILE_TYPE
      // 中英互译功能允许 jpg
      if (this.funTitle === '中英互译') {
        fileType = [...FILE_TYPE, 'jpg', 'png', 'jpeg']
      }
      const list = file.name.split('.')
      const fileSize = 50
      if (!fileType.includes(list[list.length - 1])) {
        if (msg) {
          Message.closeAll()
          // this.$message.error({
          //   message: '上传文件格式不正确'
          // })
          this.$message.warning({
            message: '上传文件格式不正确',
            duration: 3000
          })
        }

        return false
      }
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        if (msg) {
          Message.closeAll()
          // this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
          this.$message.warning({ message: `上传文件大小不能超过 ${fileSize}MB!`, duration: 3000 })
        }

        return false
      }
      return isLt2M
    },
    onChange(file, fileList) {
      console.log(file, 'change', fileList)

      if (!this.beforeAvatarUpload(file)) return false
      const list = fileList.filter((file) => {
        return this.beforeAvatarUpload(file, false)
      })
      // 生成图片缩略图
      list.forEach((f) => {
        const ext = f.name ? f.name.split('.').pop().toLowerCase() : ''
        if (['jpg', 'jpeg', 'png'].includes(ext)) {
          const reader = new FileReader()
          reader.onload = (e) => {
            f.previewUrl = e.target.result
          }
          reader.readAsDataURL(f.raw || f)
        }
      })
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.timer = null
        this.$emit('sendComFileUploadDialog', list)
        this.showDialog = false
      }, 100)
      //       this.$emit("sendComFileUploadDialog", list);
      //       this.showDialog = false;
    },
    handleExceed(files, fileList) {
      this.$message.closeAll()
      this.$message.warning({ message: `当前限制选择8个文件，本次选择了 ${files.length} 个文件，共选择了 ${fileList.length} 个文件`, duration: 3000 })
    },
    closeClick() {
      this.$emit('sendComFileUploadClose')
    }
  }
}
</script>

<style lang="scss">
.comFileUploadDialog {
  margin-bottom: 10px;
  padding: 20px;
  background-color: var(--color-primary-light);
  border-radius: 20px;
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
    }
    .el-upload-dragger {
      border: 1px solid var(--color-primary);
      background-color: var(--color-primary-light);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .svg-icon {
        font-size: 24px;
      }
      .upload-title-dark {
        margin-top: 6px;
        font-weight: 400;
        font-size: 18px;
      }
      .upload-title-light {
        margin-top: 6px;
        font-weight: 400;
        font-size: 14px;
        color: #898787;
      }
      .upload-btn {
        margin-top: 6px;
        font-weight: 400;
        font-size: 14px;
        color: #fff;
        display: inline-block;
        background-color: var(--color-primary);
        padding: 6px;
        border-radius: 10px;
      }
    }
  }
  .title {
    font-weight: bold;
  }
}
</style>
