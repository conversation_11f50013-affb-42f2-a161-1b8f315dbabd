<template>
  <div class="my-sel">
    <div v-for="v in list" class="my-option" @click="itemClick(v.value)">
      <div>{{ v.label }}</div>
      <svg-icon v-if="value == v.value" icon-class="speed-selected" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'ComSelfSel',
  props: {
    value: { type: [String, Number] },
    list: { type: [Array, String] }
  },
  methods: {
    itemClick(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    }
  }
}
</script>

<style scoped lang="scss">
  .my-sel{
    .my-option{
      cursor: pointer;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #DCDCDC;
    }
  }
</style>
