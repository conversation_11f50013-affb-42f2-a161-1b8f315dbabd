<template>
  <el-dialog :center="true" title="上下文预览3条" :visible.sync="showDialog" :width="width" modal-append-to-body>
    <div v-for="v in list">
      <div class="contentEa" :class="{'humanEa': v.obj == 'Human'}">
        <div>{{ v.obj }}</div>
        <div>{{ v.value }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getResData } from '@/api/deepSeekApi'

export default {
  name: 'HistoryPreviewDialog',
  props: {
    list: { type: [Array, String] },
    info: { type: [Object, String] },
    appId: { type: [String] },
    chatId: { type: [String] },
    width: { type: String, default: '600px' }
  },
  data() {
    return {
      showDialog: false
    }
  },
  methods: {
    show() {
      this.showDialog = true
      this.$nextTick(() => {
        this.init()
      })
    },
    init() {
      getResData({
        appId: this.appId,
        chatId: this.chatId,
        dataId: this.info.dataId
      }).then(res => {
        this.list = res.data[res.data.length - 1].historyPreview
      })
    }
  }
}
</script>

<style scoped lang="scss">
.contentEa{
  padding: 5px;
  border: 1px solid #E8EBF0;
  border-radius: 5px;
}
.humanEa{
  background-color: hsla(210,6%,76%,.7)!important;
}
</style>
