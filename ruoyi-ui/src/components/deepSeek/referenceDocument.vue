<template>
  <el-drawer
    class="quote-file-container"
    :center="true"
    :visible.sync="showDialog"
    :size="disabledTrigger?'80%':'45%'"
    modal-append-to-body
    :before-close="handleClose"
  >
    <div slot="title" class="quote-title">参考资料</div>
    <div class="quote-content">
      <div v-for="(item,index) in list" :key="index" class="quote-item">
        <div class="quote-item-title" @click="handleClick(item)">
          <span v-html=" fileSvgIcon(item.sourceName,16,16) " />{{ formatFilenameWithTimestamp(item.sourceName) }}
        </div>
        <!-- <a :href="item.url" class="quote-item-content" target="_blank" rel="noopener noreferrer"> -->
        <!-- <span v-html="md.render(item.inforList[0].q)" /> -->
        <!-- <div class="quote-item-text">{{ extractChineseText(item.inforList[0].q) }}</div> -->
        <!-- </a> -->
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { fileSvgIcon, formatFilenameWithTimestamp } from '@/utils/deepseek'
import MarkdownIt from 'markdown-it'
import MarkdownItAbbr from 'markdown-it-abbr'
import MarkdownItAnchor from 'markdown-it-anchor'
import MarkdownItFootnote from 'markdown-it-footnote'
// import MarkdownItHighlightjs from 'markdown-it-highlightjs'
import MarkdownItSub from 'markdown-it-sub'
import MarkdownItSup from 'markdown-it-sup'
import MarkdownItTasklists from 'markdown-it-task-lists'
import MarkdownItTOC from 'markdown-it-toc-done-right'
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
}).use(MarkdownItAbbr)
  .use(MarkdownItAnchor)
  .use(MarkdownItFootnote)
  .use(MarkdownItSub)
  .use(MarkdownItSup)
  .use(MarkdownItTasklists)
  .use(MarkdownItTOC)
export default {
  name: 'InternetFile',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: md,
      showDialog: false,
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile'
    }
  },
  created() {
    this.showDialog = this.value
  },
  methods: {
    formatFilenameWithTimestamp,
    fileSvgIcon,
    handleClick(item) {
      this.showDialog = false
      this.$emit('handleReadPower', item)
    },
    handleClose() {
      this.showDialog = false
      this.$emit('update:value', false)
    },
    extractChineseText(markdownText) {
      // 先将 Markdown 转换为 HTML
      const htmlText = this.md.render(markdownText)
      // 再从 HTML 中提取纯文本
      const div = document.createElement('div')
      div.innerHTML = htmlText
      const textContent = div.textContent || div.innerText
      // 使用正则表达式提取中文字符
      const chineseText = textContent.match(/[\u4e00-\u9fa5]/g)
      return chineseText ? chineseText.join('') : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.quote-title{
font-weight: 600;
font-size: 20px;
color: #404040;
}
.quote-content{
  padding:0 16px 20px 16px;

  .quote-item{
    padding: 10px 0;
    .quote-item-content{
      font-size: 14px;
      color:#909399;
      font-weight:400;
      line-height:24px;
    }
  }
}
.quote-item-title{
  // font-weight: 600;
  // font-size: 17px;
  // color: #404040;
  // line-height:30px;
  // font-family: PingFang SC;
  font-weight: 400;
  font-style: Medium;
  font-size: 16px;
  letter-spacing: 5%;
  color:#5E5E5E;
  cursor: pointer;
}
.quote-item-text{
  font-weight: 400;
  font-style: Medium;
  font-size: 12px;
  letter-spacing: 5%;
  color:#9C9C9C;
  // 添加限制3行显示并显示省略号的样式
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;

}
</style>
