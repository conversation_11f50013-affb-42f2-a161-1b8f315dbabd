<template>
  <div class="file-preview" :class="{ 'file-preview-img': showImg }">
    <svg-icon v-if="!showImg" :icon-class="icon" class="file-icon" />
    <div
      v-if="!showImg"
      class="file-container"
      :class="{
        'flex-center':!fileSize,
      }"
    >
      <el-tooltip effect="dark" :content="file.name">
        <div class="file-name">{{ file.name }}</div>
      </el-tooltip>

      <div v-if="fileSize" class="file-size">
        <span>{{ fileSize }}</span>
      </div>
    </div>
    <img v-if="showImg" class="file-img" :src="file.previewUrl">
    <el-progress
      v-if="showProgress"
      class="file-progress"
      :width="50"
      type="circle"
      :percentage="file.percentage"
      :format="
        (percentage) => {
          return parseInt(percentage);
        }
      "
    />
  </div>
</template>
<script>
import { FILE_TYPE } from '@/utils/constant'
export default {
  name: 'FilePreview',
  props: {
    file: {
      type: Object,
      default: () => {
        return {}
      }
    },
    imgType: {
      type: Array,
      default: () => {
        return []
      }
    },
    progress: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    icon() {
      const fileType = this.file.name.split('.').pop().toLowerCase()
      if (['docx', 'doc'].includes(fileType)) {
        return 'docx'
      } else if (['pdf'].includes(fileType)) {
        return 'pdf'
      } else if (['xlsx', 'xls'].includes(fileType)) {
        return 'xlsx'
      } else if (['pptx', 'ppt'].includes(fileType)) {
        return 'pptx'
      } else if (['txt'].includes(fileType)) {
        return 'txt'
      } else {
        return 'other'
      }
    },
    fileSize() {
      if (this.file.size === undefined) {
        return false
      }
      if (this.file.size < 1024) {
        return this.file.size + 'B'
      } else if (this.file.size < 1024 * 1024) {
        return (this.file.size / 1024).toFixed(2) + 'KB'
      } else if (this.file.size < 1024 * 1024 * 1024) {
        return (this.file.size / (1024 * 1024)).toFixed(2) + 'MB'
      } else {
        return (this.file.size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
      }
    },
    showProgress() {
      if (!this.progress) {
        return false
      }
      if (this.file.response) {
        return this.file.percentage && this.file.percentage < 100
      } else {
        return true
      }
    },
    showImg() {
      if (Array.isArray(this.imgType) && this.imgType.length) {
        const fileType = this.file.name.split('.').pop().toLowerCase()
        return this.imgType.includes(fileType)
      } else {
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.file-preview {
  cursor: pointer;
  position: relative;
  width: 175px;
  display: inline-flex;
  padding: 8px;
  border-radius: 8px;
  // background: #fff;
  background: linear-gradient(90deg, #f6f6f6, #ffffff);
  box-shadow: 0px 2px 10px 0px rgba(18,47,96,.2);
  border:2px solid #fff;
  // border: 1px solid var(--color-primary);
  .file-icon {
    font-size: 40px;
    margin-right: 6px;
  }
  .file-container {
    width: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px 0;
    .file-name {
      font-weight: 400;
      font-size: 14px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #262626;
      text-align: left;
    }
    .file-size {
      font-weight: 400;
      font-size: 12px;
      color: #b4b4b4;
      text-align: left;
    }
  }
  .flex-center{
    justify-content: center;
  }
  .file-progress{
    position: absolute;
    left:50%;
    top:50%;
    transform: translate(-50%,-50%);
    z-index: 10;
  }
}
div.file-preview-img{
  width:60px;
  padding:0;
 &>img.file-img{
    width:60px !important;
    height:60px !important;
    border-radius: 8px;
    border: 1px solid var(--color-primary);
  }
}
</style>
