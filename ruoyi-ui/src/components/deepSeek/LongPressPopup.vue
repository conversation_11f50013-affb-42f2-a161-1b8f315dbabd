<template>
  <div class="long-press-container">
    <!-- 长按目标元素 -->
    <div
      class="press-target"
      @touchstart.stop="handleTouchStart"
      @touchmove.stop="handleTouchMove"
      @touchend.stop="handleTouchEnd"
      @touchcancel.stop="handleTouchCancel"
      @click.stop="handleClick"
      ref="target"
    >
      <slot name="target"></slot>
    </div>

    <!-- 弹出选择框 -->
    <div
      v-if="showPopup"
      ref="popup"
      class="popup"
      :style="popupStyle"
      @click.stop
      @touchstart.stop
    >
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LongPressPopup',
  props: {
    pressDuration: {
      type: Number,
      default: 500
    },
    offset: {
      type: Number,
      default: 10
    },
    minWidth: {
      type: Number,
      default: 120
    },
    animationDuration: {
      type: Number,
      default: 200
    },
    // 滑动阈值（超过这个距离则认为是滑动而非长按）
    swipeThreshold: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      showPopup: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      pressTimer: null,
      isPressing: false,
      isSwiping: false, // 标记是否正在滑动
      popupStyle: {
        top: '0px',
        left: '0px',
        minWidth: '120px',
        transform: 'translate(-50%, 0) scale(0.9)',
        opacity: 0,
        transition: 'all 0.2s ease'
      }
    }
  },
  mounted() {
    document.addEventListener('touchstart', this.handleExternalTouch, true)
    window.addEventListener('scroll', this.handleScroll)
  },
  beforeDestroy() {
    document.removeEventListener('touchstart', this.handleExternalTouch, true)
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    closePopup() {
      this.showPopup = false;
    },

    handleTouchStart(e) {
      // 重置状态
      this.isPressing = true
      this.isSwiping = false

      // 记录初始触摸位置
      this.startX = e.touches[0].clientX
      this.startY = e.touches[0].clientY
      this.currentX = this.startX
      this.currentY = this.startY

      // 清除之前的定时器
      if (this.pressTimer) {
        clearTimeout(this.pressTimer)
      }

      // 设置长按定时器
      this.pressTimer = setTimeout(() => {
        // 只有在没有滑动且仍在按压状态下才显示弹窗
        if (this.isPressing && !this.isSwiping) {
          this.showPopup = true
          this.setInitialAnimationState()
          this.$nextTick(() => {
            this.calculatePosition()
          })
        }
      }, this.pressDuration)
    },

    // 新增：处理触摸移动事件，检测是否为滑动
    handleTouchMove(e) {
      if (!this.isPressing) return

      // 更新当前触摸位置
      this.currentX = e.touches[0].clientX
      this.currentY = e.touches[0].clientY

      // 计算移动距离
      const diffX = Math.abs(this.currentX - this.startX)
      const diffY = Math.abs(this.currentY - this.startY)

      // 如果移动距离超过阈值，则认为是滑动
      if (diffX > this.swipeThreshold || diffY > this.swipeThreshold) {
        this.isSwiping = true
        clearTimeout(this.pressTimer) // 清除长按定时器
      }
    },

    handleTouchEnd() {
      this.isPressing = false
      clearTimeout(this.pressTimer)
    },

    handleTouchCancel() {
      this.isPressing = false
      this.isSwiping = false
      clearTimeout(this.pressTimer)
      this.showPopup = false
    },

    handleClick() {
      if (this.showPopup) {
        this.showPopup = false
      }
    },

    handleExternalTouch(e) {
      if (!this.showPopup) return

      const target = this.$refs.target
      const popup = this.$refs.popup

      const isTouchOnTarget = target && target.contains(e.target)
      const isTouchOnPopup = popup && popup.contains(e.target)

      if (!isTouchOnTarget && !isTouchOnPopup) {
        this.showPopup = false
        e.stopPropagation()
      }
    },

    handleScroll() {
      if (this.showPopup) {
        this.showPopup = false
      }
    },

    setInitialAnimationState() {
      this.popupStyle = {
        ...this.popupStyle,
        top: `${this.startY}px`,
        left: `${this.startX}px`,
        transform: 'translate(-50%, -50%) scale(0.9)',
        opacity: 0,
        transition: `all ${this.animationDuration}ms cubic-bezier(0.34, 1.56, 0.64, 1)`
      }
    },

    calculatePosition() {
      const popup = this.$refs.popup
      if (!popup) return

      const popupWidth = popup.offsetWidth
      const popupHeight = popup.offsetHeight
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight

      let left = this.startX
      if (left + popupWidth / 2 > screenWidth) {
        left = screenWidth - popupWidth / 2
      }
      if (left - popupWidth / 2 < 0) {
        left = popupWidth / 2
      }

      let top = this.startY
      let transform = ''

      if (this.startY + popupHeight + this.offset > screenHeight) {
        transform = `translate(-50%, calc(-100% - ${this.offset}px)) scale(1)`
      } else {
        transform = `translate(-50%, ${this.offset}px) scale(1)`
      }

      this.popupStyle = {
        ...this.popupStyle,
        top: `${top}px`,
        left: `${left}px`,
        minWidth: `${this.minWidth}px`,
        transform: transform,
        opacity: 1
      }
    }
  }
}
</script>

<style scoped>
.press-target {
  touch-action: manipulation;
  user-select: none;
}

.popup {
  position: fixed;
  z-index: 9999;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  pointer-events: auto;
}

.popup-leave-active {
  transition: all 150ms ease-out;
  opacity: 0 !important;
  transform: translate(-50%, 0) scale(0.9) !important;
}

.popup-item {
  padding: 12px 20px;
  font-size: 14px;
  color: #333;
  text-align: left;
  white-space: nowrap;
  cursor: pointer;
}

.popup-item:active {
  background-color: #f5f5f5;
}
</style>
