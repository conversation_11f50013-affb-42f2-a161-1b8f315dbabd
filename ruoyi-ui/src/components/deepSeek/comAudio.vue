<template>
  <div>
    <!-- <div v-if="deviceType === 'pc'">
      <div class="el-icon-microphone pointer pc-recorder" @click="startRecording"></div>
      <div
        v-if="isRecording"
        class="el-icon-video-pause pointer"
        @click="stopRecording('cancel')"
      ></div>
      <div v-if="isRecording" class="el-icon-check pointer" @click="stopRecording('finish')"></div>
      <audio v-if="false" :src="audioURL" controls></audio>
    </div> -->
    <div class="mobile-display">
      <div v-if="!isRecording" class="micro-icon">
        <el-tooltip content="语音输入" placement="top" effect="dark">
          <span>
            <svg-icon
              icon-class="recordFill"
              style="font-size: 22px; color: #c9d5e3"
              class=" pointer"
              @click="startRecording"
            />
          </span>
        </el-tooltip>
      </div>
      <div v-else class="recording-icon-group">
        <div>
          <img
            src="@/assets/images/sound-wave.gif"
            alt=""
            style="height: 15px"
          >
          <img
            src="@/assets/images/sound-wave.gif"
            alt=""
            style="height: 15px"
          >
          <img
            src="@/assets/images/sound-wave.gif"
            alt=""
            style="height: 15px"
          >
        </div>
        <div>
          <el-tooltip content="取消录制" placement="top" effect="dark">
            <span>
              <svg-icon
                icon-class="cancelSpeak"
                style="font-size: 24px"
                class=" pointer"
                @click="stopRecording('cancel')"
              />
            </span>
          </el-tooltip>
        </div>
        <div>
          <el-tooltip content="完成语音输入" placement="top" effect="dark">
            <span>
              <svg-icon
                icon-class="finishSpeak"
                style="font-size: 24px"
                class=" pointer"
                @click="stopRecording('finish')"
              />
            </span>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { transcriptions } from '@/api/deepSeekApi'
import UAParser from 'ua-parser-js'

export default {
  name: 'ComAudio',
  data() {
    return {
      mediaRecorder: null,
      audioChunks: [],
      audioURL: null,
      isRecording: false,
      isMobile: false,
      isTablet: false,
      isDesktop: false,
      deviceType: 'pc',
      status: 'finish'
    }
  },
  created() {
    this.detectDevice()
    if (this.isMobile || this.isTablet) {
      this.deviceType = 'mobile'
    }
  },
  methods: {
    detectDevice() {
      const parser = new UAParser()
      const result = parser.getResult()
      this.isMobile = result.device.type === 'mobile'
      this.isTablet = result.device.type === 'tablet'
      this.isDesktop = !this.isMobile && !this.isTablet
    },
    async startRecording() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true
        })
        this.mediaRecorder = new MediaRecorder(stream)
        this.audioChunks = []
        this.mediaRecorder.ondataavailable = (e) => {
          this.audioChunks.push(e.data)
        }
        this.mediaRecorder.onstop = () => {
          console.log('Recording stopped')
          if (this.status === 'cancel') {
            this.isRecording = false
            return
          }
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/mpeg' })
          this.uploadAudio(audioBlob)
          this.audioURL = URL.createObjectURL(audioBlob)
          this.isRecording = false
        }
        this.mediaRecorder.start()
        this.isRecording = true
      } catch (error) {
        console.error('Error accessing the microphone', error)
      }
    },
    stopRecording(status) {
      this.status = status
      if (this.mediaRecorder) {
        this.mediaRecorder.stop()
      }
    },
    cancelRecording() {},
    playAudio() {
      const audio = new Audio(this.audioURL)
      audio.play()
    },
    uploadAudio(audioBlob) {
      console.log('uploading audio', audioBlob)
      const formData = new FormData()
      formData.append('file', audioBlob, '1.mp3')
      transcriptions(formData)
        .then((response) => {
          this.$emit('transAudioSuccess', response.text)
        })
        .catch((error) => {
          this.$message.warning({ message: error.response.data.message, duration: 3000 })
        })
    }
  }
}
</script>
<style scoped lang="scss">
button {
  margin-right: 10px;
}
.mobile-display {
  .micro-icon {
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .recording-icon-group {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    width: 150px;
  }
}
</style>
