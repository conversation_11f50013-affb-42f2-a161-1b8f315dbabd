<template>
  <el-popover
    ref="popover1"
    placement="top"
    trigger="click"
    popper-class="speed-container"
    class="speed-list-popover"
  >
    <div class="speed-list-container">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="speed-item"
        @click="handleSpeedChange(item)"
      >
        <span>{{ item.label }}</span>
        <svg-icon
          v-show="val === item.value"
          icon-class="speed-selected"
        />
      </div>
    </div>
    <div slot="reference" class="speed-button">倍速</div>
  </el-popover>
</template>
<script>
export default {
  name: 'SelectSpeed',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Number, String],
      default: '1'
    },
    list: {
      type: Array,
      default: () => {
        return [
          { label: '1.5X', value: '1.5' },
          { label: '1.25X', value: '1.25' },
          { label: '1.0X', value: '1' },
          { label: '0.5X', value: '0.5' }
        ]
      }
    }
  },
  data() {
    return {
      val: 1
    }
  },
  watch: {
    value() {
      this.initData()
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.val = this.value
    },
    handleSpeedChange(item) {
      if (item.value !== this.val) {
        this.val = item.value
        this.$emit('change', item.value)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.speed-button{
font-weight: 400;
font-size: 14px;
color:var(--color-primary);
display:inline-block;
padding:3px 6px;
border-radius: 4px;
box-shadow: 0px 4px 4px 0px #DCDDDF;
background: var(--color-primary-light);
}

.speed-list-container{
   .speed-item{
     display: flex;
     justify-content: space-between;
     align-items: center;
      font-weight: 400;
      font-size: 12px;
      color:#4C4C4C;
     padding:8px 0;
     border-bottom: 1px solid #DCDCDC;
   }
   .speed-item:last-child{
     border-bottom: none;
   }
}
</style>
