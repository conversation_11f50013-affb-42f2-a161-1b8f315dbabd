<template>
  <el-drawer
    class="quote-file-container"
    :center="true"
    :show-close="true"
    :visible.sync="showDialog"
    :size="disabledTrigger?'80%':'45%'"
    modal-append-to-body
    :before-close="handleClose"
    :title="name"
  >
    <!-- <div slot="title" class="quote-title">{{ name }}</div> -->
    <div class="quote-content">
      <!-- <div v-for="(item,index) in list" :key="index" class="quote-item" v-html="item.q" /> -->
      <div class="quote-item" v-html="textInfor" />
    </div>
  </el-drawer>
</template>
<script>
import MarkdownIt from 'markdown-it'
import MarkdownItAbbr from 'markdown-it-abbr'
import MarkdownItAnchor from 'markdown-it-anchor'
import MarkdownItFootnote from 'markdown-it-footnote'
import MarkdownItHighlightjs from 'markdown-it-highlightjs'
import MarkdownItSub from 'markdown-it-sub'
import MarkdownItSup from 'markdown-it-sup'
import MarkdownItTasklists from 'markdown-it-task-lists'
import MarkdownItTOC from 'markdown-it-toc-done-right'
const md = MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
})
  .use(MarkdownItAbbr)
  .use(MarkdownItAnchor)
  .use(MarkdownItFootnote)
  .use(MarkdownItHighlightjs)
  .use(MarkdownItSub)
  .use(MarkdownItSup)
  .use(MarkdownItTasklists)
  .use(MarkdownItTOC)

export default {
  name: 'QuoteFile',
  props: {
    list: {
      type: Array,
      default: []
    },
    value: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showDialog: false,
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile',
      rawMarkdown: '', // Markdown内容。
      textInfor: ''
    }
  },
  watch: {
    list: {
      handler(cur) {
        // this.rawMarkdown = cur.map(i => {
        //   return {
        //     q: md.render(i.q)
        //   }
        // })
        this.rawMarkdown = ''
        cur.forEach(i => {
          this.rawMarkdown += i.q
        })
        this.textInfor = md.render(this.rawMarkdown)
        // console.log('this.rawMarkdown', this.rawMarkdown, this.textInfor)
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.showDialog = this.value
  },
  methods: {
    handleClose() {
      this.showDialog = false
      this.$emit('update:value', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.quote-title{
font-weight: 600;
font-size: 20px;
color: #404040;
}
.quote-content{
  padding:0 16px 20px 16px;
  font-size: 16px;
  color:#262626;
  font-weight:400;
  .quote-item{
    padding: 10px 0;
    line-height:30px;
  }
}
::v-deep .el-drawer__header{
  font-weight: 600;
  color:#333;
  background-color: #fafafa;
  padding: 14px;
  margin-bottom: 0px;
}
::v-deep .el-button--primary.is-plain{
  background-color: #4CB848;
}
::v-deep .el-button--primary.is-plain span{
  color:#fff;
}
::v-deep .quote-item {
  /* 为整个表格设置样式 */
  table {
    width: 100%;
    border-collapse: collapse; /* 合并表格边框 */
    margin: 20px 0; /* 表格上下外边距 */
    font-size: 16px; /* 字体大小 */
    text-align: left; /* 文本左对齐 */
  }

  /* 为表格行设置样式 */
  tr {
    border-bottom: 1px solid #ddd; /* 表格行底部边框 */
  }

  /* 为表格单元格设置样式 */
  td {
    padding: 8px; /* 单元格内边距 */
    border: 1px solid #ddd; /* 单元格边框 */
  }

  /* 为表头单元格设置特殊样式 */
  th {
    background-color: #f2f2f2; /* 表头背景色 */
    padding: 12px; /* 表头内边距 */
    border: 1px solid #ddd; /* 表头边框 */
    text-align: left; /* 表头文本左对齐 */
  }

  /* 悬停效果：当鼠标悬停在表格行上时改变背景色 */
  tr:hover {
    background-color: #f9f9f9; /* 悬停行背景色 */
  }
}
</style>
