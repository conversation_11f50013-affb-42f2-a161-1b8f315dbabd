<template>
  <div class="computer-audio">
    <el-tooltip v-show="!isRecording" content="语音输入" placement="top" effect="dark">
      <span>
        <svg-icon
          icon-class="recordFill"
          class="audio-icon pointer"
          @click="startRecording"
        />
      </span>
    </el-tooltip>
    <el-tooltip v-show="isRecording" placement="top" content="停止语音输入" effect="dark">
      <div class="computer-audio-wave pointer" @click="stopRecording">
        <audio-wave :num="3" />
      </div>
    </el-tooltip>
  </div>
</template>
<script>
import { transcriptions } from '@/api/deepSeekApi'
import AudioWave from './audio-wave.vue'
export default {
  name: 'ComputerAudio',
  components: {
    AudioWave
  },
  data() {
    return {
      isRecording: false,
      mediaRecorder: null
      // partialText: ''
    }
  },
  destroyed() {
    if (this.mediaRecorder) {
      this.mediaRecorder = null
    }
  },
  methods: {
    async startRecording() {
      this.isRecording = true
      // this.partialText = '' // 新增字段用于保存逐步识别出的内容

      try {
        const stream = await navigator?.mediaDevices?.getUserMedia({
          audio: true
        })
        const audioTracks = stream.getAudioTracks()
        audioTracks.forEach(track => {
          console.log('当前音频输入设备:', track.label)
        })
        // this.mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/webm' })
        this.mediaRecorder = new MediaRecorder(stream)
        const audioChunks = []
        // 每 1000ms 触发一次 ondataavailable，即每秒上传一次音频片段
        this.mediaRecorder.ondataavailable = (e) => {
          // audioChunks.length = 0
          if (e.data.size > 0) {
            audioChunks.push(e.data)
            const audioBlob = new Blob(audioChunks, { type: 'audio/mpeg' })
            this.uploadAudio(audioBlob)
            // const partialBlob = new Blob([e.data], { type: 'audio/mpeg' })
            // console.log('partialBlob', partialBlob)
            // this.uploadAudio(partialBlob) // 调用上传部分音频的方法
          }
        }
        this.mediaRecorder.onstop = () => {
          console.log('Recording stopped')
          // const audioBlob = new Blob(audioChunks, { type: 'audio/mpeg' })
          // this.uploadAudio(audioBlob)
          // this.$emit('transAudioSuccess', this.partialText)
          // this.partialText = ''
          this.isRecording = false
          this.mediaRecorder = null
        }
        // 每隔 1 秒触发一次 ondataavailable
        this.mediaRecorder.start(1000)
        this.isRecording = true
      } catch (error) {
        console.error('Error accessing the microphone', error)
      }
    },
    uploadAudio(audioBlob) {
      // console.log('uploading audio', audioBlob)
      const formData = new FormData()
      formData.append('file', audioBlob, '1.mp3')
      transcriptions(formData)
        .then((response) => {
          // if (!response.text || !response.text.trim()) {
          //   this.$message.warning('您还未说话，请重新发送语音')
          //   return
          // }
          // this.partialText += response.text + ' '
          this.$emit('transAudioSuccess', response.text)
          // this.$emit('transAudioSuccess', this.partialText)
        })
        .catch((error) => {
          console.log('error', error)
          // this.$message.error(error)
        })
    },
    stopRecording() {
      if (this.isRecording && this.mediaRecorder) {
        this.mediaRecorder.stop()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
 .computer-audio{
  display:inline-block;
 }
 .audio-icon{
  font-size: 22px;
   color: #c9d5e3;
 }
 .computer-audio-wave{
   width:30px;
   height: 30px;
   border-radius: 4px;
   border:1px solid var(--color-primary);
   background: var(--color-primary-light);
   display:inline-flex;
   justify-content: center;
   align-items: center;
 }
</style>
