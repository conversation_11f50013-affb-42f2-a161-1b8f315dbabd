<template>
  <el-dialog
    title="结果反馈"
    :visible.sync="showDialog"
    :width="width"
    modal-append-to-body
    class="my-custom-alert"
    :close-on-click-modal="false"
  >
    <!-- 添加空验证 -->
    <el-input
      v-model="textarea"
      rows="8"
      height="210px"
      type="textarea"
      maxlength="200"
      show-word-limit
      placeholder="请输入反馈内容"
    />
    <div class="el-message-box__btns">
      <!-- <el-button @click="showDialog = false">关闭</el-button> -->
      <button type="button" class="el-button el-button--default el-button--small" @click="showDialog = false">
        <span>
          取消
        </span>
      </button>
      <!-- 内容为空时无法提交 -->
      <!-- :disabled="!textarea.trim()" -->
      <!-- <el-button
        type="primary"
        @click="submit"
      >提交反馈</el-button> -->
      <button type="button" class="el-button el-button--default el-button--small el-button--primary " @click="submit">
        <span>
          提交反馈
        </span>
      </button>
    </div>
  </el-dialog>
</template>

<script>
import { updateUserFeedback } from '@/api/deepSeekApi'
import { eventBus } from '@/utils/eventBus'
import { set } from 'nprogress'

export default {
  name: 'ComBadDialog',
  props: {
    info: { type: Object },
    chatId: { type: String }
  },
  data() {
    return {
      textarea: '',
      showDialog: false,
      width: localStorage.getItem('deviceType') === 'mobile' ? '80%' : '400px'
    }
  },
  methods: {
    show() {
      this.showDialog = true
      this.$nextTick(() => {
        this.init()
      })
    },
    init() {},
    submit() {
      // setTimeout(() => {
      //   updateUserFeedback({
      //     chatId: this.chatId,
      //     dataId: this.info.dataId,
      //     userBadFeedback: this.textarea
      //   }).then(res => {
      //   })
      // }, 2000)
      updateUserFeedback({
        chatId: this.chatId,
        dataId: this.info.dataId,
        userBadFeedback: this.textarea || 'yes'
      }).then((res) => {
        this.$set(this.info, 'userBadFeedback', this.textarea || 'yes')
        if (res.code === 200) {
          this.$message.closeAll()
          this.$message.success({ message: '提交成功', duration: 3000 })
        } else {
          this.$message.closeAll()
          this.$message.warning({ message: '提交失败', duration: 3000 })
        }
        this.showDialog = false
        this.textarea = ''
        this.$emit('sendComBadDialog')
        // ✅ 添加积分刷新事件
        setTimeout(() => {
          eventBus.$emit('refreshPoints')
        }, 200)
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  .el-dialog__header{
    text-align: center;
    font-weight: bold;
    .el-dialog__title{
      font-size: 20px;
      color: black;
    }
  }
  .el-input--medium{
    padding: 10px 20px 20px;
    textarea{
      background:#fff!important;
      border: 1px solid #DCDFE6;
    }
  }
}
::v-deep .el-textarea{
  .el-input__count{
    right: 30px;
    bottom:25px;
  }
}
::v-deep .el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  padding: 0!important;
}
::v-deep .el-dialog{
  border-radius: 4px!important;
}
::v-deep .el-dialog__headerbtn{
  top:16px;
}
::v-deep .el-message-box__btns{
  height: 48px;
}
</style>
