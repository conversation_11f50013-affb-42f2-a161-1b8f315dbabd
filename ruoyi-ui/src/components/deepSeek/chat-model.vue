<template>
  <div class="chat-model-container">
    <el-dropdown placement="top" trigger="click" @command="handleCommand">
      <div class="chat-model pointer" :style="{ background: bg, color: '#4cb848' }">
        <!-- <svg-icon icon-class="refresh" class="mr-6" /> -->
        <svg-icon v-show="!isMobile" icon-class="refresh" class="mr-6" />
        <span>{{ modelName }}</span>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="(item, i) of list" :key="i" :command="item.value" :divided="!!i">
            <div class="list">
              <div class="left">
                <span class="label">{{ item.label }}</span>
                <div>
                  <span class="msg">{{ item.msg }}</span>
                </div>
              </div>
              <div class="right">
                <i v-if="val === item.value" class="el-icon-check check-icon" />
              </div>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 知识库类型 -->
    <el-dropdown v-show="!['中英互译', '文档校对', '文档总结', '文档阅读', '提示词帮助', 'excel助手'].includes(funTitle)" placement="top" trigger="click" @command="handleKnowledgeTypeChange">
      <div class="chat-online1 pointer ml-10" :style="{ background: bg, color: '#4cb848' }">
        <svg-icon v-show="!isMobile" icon-class="knowledge" class="mr-6" :style="{ background: bg, color: '#4cb848' }" />
        <span>{{ knowledgeTypeName }}</span>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(type, index) in knowledgeTypes"
            :key="index"
            :command="type.value"
          >
            <div class="list">
              <div class="left">
                <span class="label">{{ type.label }}</span>
              </div>
              <div class="right">
                <i v-if="knowledgeType === type.value" class="el-icon-check check-icon" />
              </div>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <div
      class="chat-online pointer ml-10"
      :style="style"
      :class="{
        'active-btn': [1, '1'].includes(online)
      }"
      @click="toggleOnline"
    >
      <!-- <svg-icon icon-class="internet" class="mr-6" /> -->
      <svg-icon v-show="!isMobile" icon-class="internet" class="mr-6" />
      <span>联网搜索</span>
    </div>
  </div>
</template>
<script>
// api
import * as api from '@/api/deepSeekApi'
export default {
  name: 'ChatModel',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    funTitle: {
      type: [String],
      default: ''
    },
    value: {
      type: [String, Number],
      default: 1
    },
    online: {
      type: [String, Number],
      default: 0
    },
    bg: {
      type: String,
      default: '#fff'
    },
    list: {
      type: Array,
      default: () => [
        { label: 'DeepSeek-R1', value: 0, msg: '先思考后回答,解决推理问题' },
        { label: 'DeepSeek-V3', value: 1, msg: '高效便捷,精准解答' }
      ]
    },
    knowledgeType: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      val: '',
      disabledTrigger: localStorage.getItem('deviceType') === 'mobile',
      knowledgeTypes: [],
      knowledgeBg: '#fff',
      localKnowledgeType: this.knowledgeType,
      isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    }
  },
  computed: {
    modelName() {
      const obj = this.list.find((item) => item.value === this.value)
      return obj ? obj.label : this.value
    },
    style() {
      return this.online ? { background: this.bg } : {}
    },
    knowledgeTypeName() {
      const type = this.knowledgeTypes.find((t) => t.value === this.localKnowledgeType)
      if (!this.knowledgeTypesLoaded) {
        return '加载中...'
      }
      // 更新默认显示文本
      return type ? type.label : (this.localKnowledgeType || '请选择知识库')
    }
  },
  watch: {
    value() {
      this.initVal()
    },
    knowledgeType(newVal) {
      this.localKnowledgeType = newVal
      this.updateKnowledgeBg()
    }
  },
  mounted() {
    this.initVal()
    this.fetchKnowledgeTypes()
  },
  methods: {
    initVal() {
      // 从本地存储获取模型值，如果没有则默认使用V3模型（值为1）
      const storedModelValue = localStorage.getItem('chatModelValue')
      if (storedModelValue !== null) {
        // console.log('initVal 有localStorage', storedModelValue)
        this.val = Number(storedModelValue)
        this.$emit('change', Number(storedModelValue))
      } else {
        // 默认使用V3模型
        this.val = 1
        // console.log('initVal 没有localStorage 默认使用V3模型')
        this.$emit('change', 1)
      }
    },
    handleCommand(item) {
      if (item !== this.val) {
        this.val = item
        this.$emit('change', item)
        // 将选中的模型值保存到本地存储
        localStorage.setItem('chatModelValue', item)
      }
    },
    async toggleOnline() {
      let searchfuncInfoData = {}
      await api.searchfuncInfo().then((res) => {
        // console.log('searchfuncInfo', res.data)
        if (res.code === 200) {
          searchfuncInfoData = res.data
        } else {
          this.$message.closeAll()
          this.$message.warning({ message: res.msg ? res.msg : '获取联网搜索功能数据失败，请稍后再试！', duration: 3000 })
          return
        }
      })
      let internet = ''
      if (this.online === 0) {
        internet = 1
      } else if (this.online === 1) {
        internet = 0
      }
      this.$emit('update:online', internet)
      if (internet === 1) {
        // 判断PC还是移动端
        // 今日可用总数${searchfuncInfoData.maxNum}次,已使用${searchfuncInfoData.useNum}次。
        this.$alert(
          `<span class="custom-title">温馨提示</span>
             <div class="alert-content">
             使用联网搜索时，您提交的内容将发布至互联网搜索引擎，请注意内容保密，切勿上传保密信息。
            </div>`,
          '',
          {
            confirmButtonText: '已了解',
            cancelButtonText: '取消',
            showCancelButton: true,
            customClass: this.disabledTrigger
              ? 'ismobile-message-box my-custom-alert'
              : 'my-custom-alert',
            dangerouslyUseHTMLString: true,
            callback: (action) => {
              if (action === 'cancel') {
                // 用户点击了取消
                this.$emit('update:online', 0)
                return 0
              }
            }
          }
        )
      }
    },
    async fetchKnowledgeTypes() {
      try {
        const res = await api.getPermissionKnowledge()
        if (res.code === 200) {
          // 修改数据处理逻辑，直接使用knowledgeCode和knowledgeName字段
          this.knowledgeTypes = res.data.map((item) => {
            return {
              value: item.knowledgeCode,
              label: item.knowledgeName
            }
          })
          this.knowledgeTypesLoaded = true
          // 默认选择"不使用知识库"
          if (!this.localKnowledgeType && this.knowledgeTypes.length > 0) {
            // 查找"不使用知识库"选项
            const noKnowledgeOption = this.knowledgeTypes.find(item => item.value === 'SPIC-NON')
            if (noKnowledgeOption) {
              this.localKnowledgeType = 'SPIC-NON'
              this.$emit('update:knowledgeType', 'SPIC-NON')
            } else {
              // 如果没有找到，则选择第一个
              this.localKnowledgeType = this.knowledgeTypes[0].value
              this.$emit('update:knowledgeType', this.knowledgeTypes[0].value)
            }
          }
        } else {
          this.$message.closeAll()
          this.$message.warning({ message: '获取知识库类型失败', duration: 3000 })
          this.knowledgeTypesLoaded = true
        }
      } catch (error) {
        this.$message.closeAll()
        console.error('Error fetching knowledge types:', error)
        this.$message.warning({ message: '获取知识库类型失败', duration: 3000 })
        this.knowledgeTypesLoaded = true
      }
    },
    handleKnowledgeTypeChange(value) {
      if (value !== this.localKnowledgeType) {
        this.localKnowledgeType = value
        this.$emit('update:knowledgeType', value)
        this.updateKnowledgeBg()
      }
    },
    updateKnowledgeBg() {
      this.knowledgeBg = this.localKnowledgeType === '0' ? '#e6f7ff' : '#fff'
    }
  }
}
</script>
<style lang="scss" scoped>
.chat-model-container {
  display: flex;
  align-items: center;
  @media (max-width: 768px) {
    width: 100%;
  }
}
.chat-model {
  color: #4c4c4c;
  font-size: 14px;
  font-weight: 400;
  height: 32px;
  width: 138px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  box-shadow: 0px 4px 4px 0px #e1e1e1;
  @media (max-width: 768px) {
     width: auto;
     min-width: 84px;
    height: 30px;
    font-size: 12px;
  }
}
.chat-online {
  color: #4c4c4c;
  font-size: 14px;
  font-weight: 400;
  margin-left: 10px;
  height: 32px;
  width: 99px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  box-shadow: 0px 4px 4px 0px #e1e1e1;
  margin-left: 6px;
  background-color: #fff;
  @media (max-width: 768px) {
    width: auto;
    // padding: 0 8px;
    min-width: 60px;
    height: 30px;
    font-size: 12px;
    margin-left: 2px;
  }
}
.chat-online1 {

  color: #4c4c4c;
  font-size: 14px;
  font-weight: 400;
  height: 32px;
  width: 114px;
  display: flex;
  justify-content: center;
  margin-left: 10px;
  align-items: center;
  border-radius: 5px;
  box-shadow: 0px 4px 4px 0px #e1e1e1;
  margin-left: 6px;
  background-color: #fff;
  @media (max-width: 768px) {

    // width: auto;
    // padding: 0 8px;
    min-width: 70px;
    max-width: 100px;
    height: 30px;
    font-size: 12px;
    margin-left: 0px;
  }
  ::v-deep &>span{
    //只显示一行做多展示5个字，超出省略号
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.active-btn {
  color: var(--color-primary);
}

.mr-6 {
  margin-right: 4px;
}
.ml-10 {
  margin-left: 10px;
}
.check-icon {
  color: var(--color-primary);
  margin-left: 20px;
  font-size: 22px;
}
.list {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left div {
  line-height: 26px;
}
.msg {
  color: #999;
}
</style>

<style lang="scss">
.my-custom-alert {
  /* div.mobile-message-box {
    width: 80%;
  } */
  padding-bottom: 0px;
  &.ismobile-message-box {
    width: 80%;
  }

  /* 弹窗整体圆角 */
  .el-message-box {
    padding: 0;
  }
  .el-message-box,
  .mobile-message-box {
    border-radius: 20px;
    overflow: hidden;
    /* 关键：让内容和圆角贴合 */
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  }

  /* 按钮区域无圆角，底部整体圆角由外层控制 */
  .el-message-box__btns {
    display: flex;
    align-items: stretch;
    overflow: hidden;
    padding: 0;
    border-top: 1px solid #eee;
    height: 48px;
    margin: 0;
    /* 确保无外边距 */
    border-radius: 0 0 20px 20px;
  }

  /* 公共按钮样式 */
  .el-message-box__btns .el-button {
    flex: 1;
    margin: 0;
    min-width: 0;
    /* 关键：去除默认最小宽度 */
    font-size: 14px;
    height: 100%;
    border: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    /* 让内容居中 */
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
  }

  /* 取消按钮（第一个） */
  .el-message-box__btns .el-button:first-child {
    color: #333;
    border-bottom-left-radius: 20px;
    border-right: 1px solid #eee;
  }

  /* 已了解按钮（第二个） */
  .el-message-box__btns .el-button:last-child {
    color: var(--color-primary);
    border-bottom-right-radius: 20px;
  }

  .custom-title {
    display: block;
    text-align: center;
    font-weight: bold;
    font-size: 20px;
    color: black;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .alert-content {
    line-height: 1.5;
    margin-bottom: 20px;
    padding: 0 15px;
    text-align: left; /* 默认左对齐 */
    max-width: 100%;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
  }

  /* 调整消息框内容区域的内边距 */
  .el-message-box__content {
    padding: 0;
  }

  /* 确保消息框标题区域没有内边距 */
  .el-message-box__header {
    padding: 0;
  }
  .el-message-box__headerbtn {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 10px; /* 增加内边距扩大可点击区域 */
    width: 20px; /* 设置基础宽度 */
    height: 20px; /* 设置基础高度 */
    z-index: 10;
    box-sizing: content-box;
  }
}
</style>
