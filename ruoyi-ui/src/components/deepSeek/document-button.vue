<template>
  <div class="document-button">
    <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      @click="docProofreadingClick"
    >
      <div class="flex-text">
        <span>文档校对</span>
        <svg-icon icon-class="documentTest" class="icon1" />
      </div>
    </el-button>
    <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      @click="docSummaryClick()"
    >
      <div class="flex-text">
        <span>文档总结</span>
        <svg-icon icon-class="documentSum" class="icon2" />
      </div>
    </el-button>
    <!-- <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      @click="docReadClick"
    >
      <div class="flex-text">
        <span>文档阅读</span>
        <svg-icon icon-class="docReadAssist" class="icon3" />
      </div>
    </el-button> -->
    <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      style="padding:6px 15px"
      @click="docTranslateClick"
    >
      <div class="flex-text">
        <span>中英互译</span>
        <svg-icon icon-class="translate" class="icon6" />
      </div>
    </el-button>
    <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      style="padding:6px 15px"
      @click="excelAssistantClick"
    >
      <div class="flex-text">
        <span>excel助手</span>
        <svg-icon icon-class="ex1" class="icon7" />
      </div>
    </el-button>
    <!-- <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      @click="moreClick"
    >
      <div class="flex-text">
        <span>更多</span>
        <svg-icon icon-class="more" class="icon5" />
      </div>
    </el-button> -->
    <el-button
      size="mini"
      type="primary"
      plain
      round
      class="file-upload-btn"
      @click="assistantClick"
    >
      <div class="flex-text">
        <span>提示词帮助</span>
        <svg-icon icon-class="documentTip" class="icon4" />
      </div>
    </el-button>
  </div>
</template>
<script>
export default {
  name: 'DocumentButtons',
  methods: {
    /** 中英互译 */
    docTranslateClick() {
      this.$emit('docTranslateClick')
    },
    /** 文档校对 */
    docProofreadingClick() {
      this.$emit('docProofreadingClick')
    },
    /** 文档总结 */
    docSummaryClick() {
      this.$emit('docSummaryClick')
    },
    /** 文档阅读 */
    docReadClick() {
      this.$emit('docReadClick')
    },
    /** 提示词帮助 */
    assistantClick() {
      this.$emit('assistantClick')
    },
    /** EXCEL助手 */
    excelAssistantClick() {
      this.$emit('excelAssistantClick')
    },
    /** 更多 */
    moreClick() {
      this.$emit('moreClick')
    }
  }
}
</script>
<style lang="scss" scoped>
// 如果屏幕宽度小于1083px同时大于961px
@media (max-width: 1083px) and (min-width: 961px) {
  .document-button{
    .el-button:nth-last-child(1){
      margin-left: 0;
    }
  }
}
//如果屏幕端口小于等961px
@media (max-width: 961px) {
 .el-button:nth-last-child(2){
      margin-left: 0;
    }
}
.document-button {
  .file-upload-btn {
    border: none;
    box-shadow: 0px 4px 4px 0px #dfdedc;
    margin-bottom:6px;
    margin-top: 6px;
  }
  .icon1 {
    margin-left: 4px;
    font-size: 13px;
  }
  .icon2 {
    margin-left: 4px;
  }
  .icon3 {
    margin-left: 4px;
    font-size:12px;
  }
  .icon4 {
    margin-left: 4px;
  }
  .icon5 {
    margin-left: 4px;
    font-size: 14px;
  }
  .icon6 {
    margin-left: 4px;
    font-size: 16px;
  }
  .icon7{
    margin-left: 4px;
    font-size: 16px;
  }
  ::v-deep .el-button.is-round{
    border-radius: 5px;
  }
}
.document-button {
  .file-upload-btn.el-button--primary.is-plain {
    background: #F2F2F2 !important; // 灰色底
    color: #666666 !important;      // 灰色字
    border-color: #F2F2F2 !important;
  }
  // .file-upload-btn.el-button--primary.is-plain:focus
  .file-upload-btn.el-button--primary.is-plain:hover
   {
    background: var(--color-primary) !important; // 鼠标移入时底色
    color: #fff !important;      // 鼠标移入时字色
    border-color: var(--color-primary) !important;
  }
}
.flex-text{
  font-size: 14px;
}
</style>
