<template>
  <div class="comFileUploadDialog">
    <el-row class="flex">
      <div class="backBox" @click="backClick">
        <div class="backBtn">
          <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.5237 0.635986C5.07891 0.635986 0.665039 5.04985 0.665039 10.4947C0.665039 15.9395 5.07891 20.3534 10.5237 20.3534C15.9686 20.3534 20.3824 15.9395 20.3824 10.4947C20.3851 5.04985 15.9686 0.635986 10.5237 0.635986ZM12.8045 14.2573C12.968 14.4208 12.968 14.6897 12.8045 14.8532L12.0873 15.5704C11.9238 15.7338 11.6549 15.7338 11.4914 15.5704L7.43086 11.5098L6.71367 10.7926C6.5502 10.6291 6.5502 10.3602 6.71367 10.1967L7.43086 9.47954L11.4914 5.41899C11.6549 5.25552 11.9238 5.25552 12.0873 5.41899L12.8045 6.13618C12.968 6.29966 12.968 6.5686 12.8045 6.73208L9.04453 10.4947L12.8045 14.2573Z" fill="#4CB848" />
          </svg>
        </div>
        <div class="title">{{ funTitle }}</div>
      </div>

      <!-- <el-button
        style="margin-left: auto"
        type="text"
        icon="el-icon-close"
        @click="closeClick"
      /> -->
    </el-row>
    <div class="upload-text">支持：txt、doc、docx、ppt、pptx、pdf、xls、xlsx</div>
    <el-upload
      :show-file-list="false"
      style="width: 100%"
      :auto-upload="false"
      :on-change="onChange"
      :before-upload="beforeAvatarUpload"
      class="upload-demo"
      drag
      action="https://jsonplaceholder.typicode.com/posts/"
      multiple
    >
      <div class="uploadContent">
        <div class="iconBox">
          <svg-icon icon-class="upload1" class="svg-icon" />
        </div>
        <div class="upload-title-dark">将文件拖拽到此处上传</div>
      </div>

      <!-- <div class="upload-title-light">文件大小不超过10M</div> -->
      <!-- <div class="upload-title-light">支持：txt、doc、docx、ppt、pptx、pdf、xls、xlsx</div> -->
      <div class="upload-btn">从本地选择文件</div>
      <!--      <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>-->
    </el-upload>
    <!-- <div class="upload-btn">选择文件</div> -->
  </div>
</template>

<script>
import { Message } from 'element-ui'
import { FILE_TYPE } from '@/utils/constant'
export default {
  name: 'ComFileUploadDialog',
  props: {
    funTitle: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      showDialog: false,
      timer: null
    }
  },
  methods: {
    backClick() {
      this.$emit('sendComFileUploadClose')
    },
    show() {
      this.showDialog = true
      this.$nextTick(() => {
        this.init()
      })
    },
    init() {},
    beforeAvatarUpload(file, msg = true) {
      const fileType = FILE_TYPE
      const list = file.name.split('.')
      const fileSize = 10
      if (!fileType.includes(list[list.length - 1])) {
        if (msg) {
          Message.closeAll()
          // this.$message.error({
          //   message: '上传文件格式不正确'
          // })
          this.$message.warning({
            message: '上传文件格式不正确',
            duration: 3000
          })
        }

        return false
      }
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        if (msg) {
          Message.closeAll()
          // this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
          this.$message.warning({ message: `上传文件大小不能超过 ${fileSize}MB!`, duration: 3000 })
        }

        return false
      }
      return isLt2M
    },
    onChange(file, fileList) {
      console.log(file, 'change', fileList)

      if (!this.beforeAvatarUpload(file)) return false
      const list = fileList.filter((file) => {
        return this.beforeAvatarUpload(file, false)
      })
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.timer = null
        this.$emit('sendComFileUploadDialog', list)
        this.showDialog = false
      }, 100)
      //       this.$emit("sendComFileUploadDialog", list);
      //       this.showDialog = false;
    },
    closeClick() {
      this.$emit('sendComFileUploadClose')
    }
  }
}
</script>

<style lang="scss">
.comFileUploadDialog {
  margin-bottom: 10px;
  // padding: 20px;
  // padding: 22px 15px 8px;
  padding: 22px 15px 22px;
  background-color: var(--color-primary-light);
  border-radius: 20px;
  .backBox{
    overflow: hidden;
    margin-bottom: 15px;
    cursor: pointer;
    .backBtn{
      float: left;
      height: 25px;
      svg{
        margin-top: 2px;
      }
    }
    .title{
      margin-left: 5px;
      color: #4CB848;
      float: left;
      font-family: Microsoft YaHei UI;
      font-weight: 400;
      font-size: 18px;
      height: 25px;
      line-height: 25px;
      letter-spacing: 5%;
    }
  }
  .upload-text{
    font-family: Microsoft YaHei UI;
    font-weight: 400;
    font-size: 16px;
    line-height: 150%;
    letter-spacing: 5%;
    color: #898787;
    margin-bottom: 16px;
    padding:0 32px
  }
  .el-upload {
    width: 100%;
    padding: 0 29px;
    .el-upload-dragger {
      height: 82px;
      width: 100%;
      border-radius: 20px;
      border: 3px dashed var(--color-primary);
      background-color: var(--color-primary-light);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .uploadContent{
        .iconBox{
          float: left;
          .svg-icon {
            font-size: 25px;
          }
        }
        .upload-title-dark {
          line-height: 25px;
          font-weight: 400;
          color: #4CB848;
          font-size: 16px;
          float: left;
          margin-left: 10px;
        }
      }
      .upload-title-light {
        margin-top: 6px;
        font-weight: 400;
        font-size: 14px;
        color: #898787;
      }
      .upload-btn {
        margin-top: 6px;
        font-weight: 400;
        font-size: 14px;
        color: #fff;
        display: inline-block;
        background-color: var(--color-primary);
        padding: 6px;
        border-radius: 10px;
      }
    }
  }
  // .upload-btn {
  //   width: 80px;
  //   height: 32px;
  //   border-radius: 40px;
  //   font-family: Microsoft YaHei UI;
  //   font-weight: 400;
  //   font-size: 12px;
  //   margin-top: 15px;
  //   color: #fff;
  //   line-height: 32px;
  //   text-align: center;
  //   display: inline-block;
  //   background-color: var(--color-primary);
  // }
  .title {
    font-weight: bold;
  }
}
</style>
