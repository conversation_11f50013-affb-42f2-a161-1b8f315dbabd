<template>
  <div ref="input" class="base-input" :contenteditable="true" :placeholder="placeholder" :data-display-placeholder="!value" @keydown="handleKeydown" @input="handleInput" @compositionstart="handleCompositionStart" @compositionend="handleCompositionEnd">
    <span v-show="Array.isArray(list) && value">
      <span
        v-for="(item,i) of list"
        :key="i"
        :class="{
          'height-light':item.highlighted,
        }"
      >{{ item.text }}</span>
    </span>
    <span v-show="!Array.isArray(list) && value">
      {{ value }}
    </span>
  </div>
</template>
<script>
export default {
  name: 'BaseInput',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    list: {
      type: [Array, String],
      default: ''
    }
  },
  data() {
    return {
      isComposing: false
    }
  },
  watch: {
    value(val) {
      if (!val) {
        this.$refs.input.textContent = ''
      }
    }
  },
  methods: {
    handleInput(e) {
      const value = e.target.innerText.replace(/\n/g, '')
      this.$emit('input', value)
      this.$emit('update:value', value)
    },
    handleKeydown(e) {
      // 监听回车键
      if (e.keyCode === 13 && !e.shiftKey) {
        // 检查是否处于输入法组合状态
        if (this.isComposing) {
          return
        }

        // 修改：即使没有内容也触发回车事件，让父组件决定是否发送
        const value = e.target.innerText.replace(/\n/g, '')
        e.preventDefault()
        this.$emit('enter', e)
      }
    },
    handleCompositionStart(e) {
      this.isComposing = true
      this.$emit('compositionstart', e)
    },
    handleCompositionEnd(e) {
      this.isComposing = false
      this.$emit('compositionend', e)
    }
  }
}
</script>
<style lang="scss" scoped>
.base-input{
  line-height: 28px;
  width:100%;
  padding:10px;
  font-size:16px;
  font-weight:400;
  color:#262626;
  background: #f7fbf6;
}
.base-input[data-display-placeholder=true]:before{
  content: attr(placeholder);
    display: block;
    pointer-events: none;
    position: absolute;
    color: rgba(6, 7, 31, .15);
    font-size:16px;
}

.height-light{
  color:var(--color-primary)
}
</style>
