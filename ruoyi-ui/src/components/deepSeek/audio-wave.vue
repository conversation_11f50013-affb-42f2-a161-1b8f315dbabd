<template>
  <div class="audio-wave-container">
    <div
      v-for="(item) in num"
      :key="item"
      class="wave"
      :style="{
        animationDelay: `${item * 0.1}s`,
        animationDuration: `${1.5 + item * 0.1}s`
      }"
    />
  </div>
</template>

<script>
export default {
  name: 'AudioWave',
  props: {
    num: {
      type: Number,
      default: 10 // 默认波浪数量
    }
  }
}
</script>

<style scoped lang="scss">
$color:#4cb848;
.audio-wave-container {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px; /* 缩小间距让波浪更紧凑 */
  height: 100%;
  .wave {
    margin: 0 1px;
    display: block;
    width: 2px; /* 调整宽度让波浪更明显 */
    height: 6px;
    border-radius: 2px;
    background: var(--color-primary);
    animation: smooth-wave 2s ease-in-out infinite;
  }
}

@keyframes smooth-wave {
  0% {
    height: 6px;
    background: var(--color-primary);
  }
  25% {
    height: 12px; /* 中间高度 */
    background: lighten($color, 10%);
  }
  50% {
    height: 20px; /* 最大高度 */
    background: lighten($color, 20%);
  }
  75% {
    height: 12px; /* 回到中间高度 */
    background: lighten($color, 10%);
  }
  100% {
    height: 6px; /* 恢复初始高度 */
    background: var(--color-primary);
  }
}
</style>
