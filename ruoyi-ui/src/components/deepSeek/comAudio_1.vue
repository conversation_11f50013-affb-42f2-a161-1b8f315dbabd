<template>
  <div>
    <div class="el-icon-microphone pointer" @click="startRecording" />
    <div v-if="isRecording" class="el-icon-video-pause pointer" @click="stopRecording" />
    <div v-if="isRecording" class="el-icon-check pointer" @click="stopRecording" />
    <div v-if="transcript">
      <h3>识别结果:</h3>
      <p>{{ transcript }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComAudio',
  data() {
    return {
      recognition: null,
      isRecording: false,
      transcript: ''
    }
  },
  mounted() {
    if ('webkitSpeechRecognition' in window) {
      this.recognition = new webkitSpeechRecognition()
      this.recognition.continuous = true
      this.recognition.interimResults = false
      this.recognition.lang = 'zh-CN' // 设置语言为中文
      this.recognition.onstart = () => {
        console.log('开始录音')
        this.isRecording = true
      }

      this.recognition.onresult = (event) => {
        console.log('event', event)
        this.transcript = event.results[0][0].transcript
      }

      this.recognition.onerror = (event) => {
        console.error('Speech recognition error detected: ' + event.error)
      }

      this.recognition.onend = () => {
        this.isRecording = false
      }
    } else {
      console.error('Speech recognition is not supported in this browser')
    }
  },
  methods: {
    startRecording() {
      this.recognition.start()
    },
    stopRecording() {
      if (this.recognition) {
        this.recognition.stop()
        this.isisRecording = false
      }
    }
  }
}
</script>

<style scoped>
.el-icon-microphone, .el-icon-video-pause, .el-icon-check {
  font-size: 24px;
  margin-right: 10px;
  cursor: pointer;
}

h3 {
  margin-top: 20px;
}
</style>
