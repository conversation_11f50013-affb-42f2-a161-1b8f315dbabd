<template>
  <div class="static-content-container">
    <div class="static-top">
      <svg-icon icon-class="deepSeek" class="sp-deepSeek" />
      <span>Hi！我是 SPIC DeepSeek</span>
    </div>
    <div class="margin-top-10">我可以帮你搜索、答疑、写作！请注意：请勿上传</div>
    <div class="margin-top-4">任何涉密文件或敏感信息哦~</div>
    <div class="margin-top-18 ltitle">
      <!-- <svg-icon icon-class="heart" style="font-size: 18px; margin-right: 6px"></svg-icon> -->
      已解答
      <!-- <span class="font">{{ interactionTotal }}</span> -->
      <span v-if="loading" class="font loading-placeholder">--</span>
      <span v-else class="font">{{ interactionTotal }}</span>
      个问题
    </div>
  </div>
</template>
<script>
import { getInteractionTotalRequest } from '@/api/mobile'
export default {
  name: 'StaticContent',
  data() {
    return {
      // interactionTotal: '加载中...',
      interactionTotal: 0,
      loading: true
    }
  },
  async mounted() {
    try {
      const res = await getInteractionTotalRequest()
      if (res.code === 200) {
        this.interactionTotal = res.data
      }
    } catch (error) {
      console.error('获取交互总数失败:', error)
      this.interactionTotal = 0
    } finally {
      this.loading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.font {
  font-family: 'Digital7', sans-serif !important;
  font-size: 52px;
  color: #4cb848;
  font-weight: 500;
  letter-spacing: 3px;
  vertical-align: -3px;
}
.static-content-container {
  color: #343435;
  text-align: center;
  margin-bottom: 12px;
  .static-top {
    font-size: 20px;
    font-weight: bold;
    color: black;
  }
}
.ltitle {
  //底部对齐

  .svg-icon {
    margin-right: 6px;
    font-size: 18px;
  }
}
</style>
