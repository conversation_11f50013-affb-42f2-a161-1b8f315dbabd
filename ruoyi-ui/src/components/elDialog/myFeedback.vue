<template>
  <div>
    <!-- 弹窗组件 -->
    <el-dialog
      title="我的反馈"
      :visible.sync="dialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="rank-container">
        <!-- 标签页 -->
        <el-table ref="filterTable" :data="list" style="width: 100%">
          <el-table-column prop="createTime" label="时间" />
          <el-table-column prop="feedbackContent" label="反馈内容" />
          <el-table-column prop="updateTime" label="图片">
            <template slot-scope="scope">
              <div
                v-if="scope.row.imageUrls.length > 0"
                class="image-preview-wrapper"
              >
                <el-image
                  :src="scope.row.imageUrls[0]"
                  :preview-src-list="scope.row.imageUrls"
                  style="width: 50px; height: 50px"
                  fit="fill"
                />
                <span
                  v-if="scope.row.imageUrls.length > 1"
                  class="image-count"
                >+{{ scope.row.imageUrls.length - 1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="响应回复">
            <template slot-scope="scope">
              <div v-if="!scope.row.responseContentList">暂无回复</div>
              <div class="response-content">
                <div v-for="item in scope.row.responseContentList" class="item">
                  【{{ item.responseDate.split(' ')[0] }}】:
                  {{ item.responseContent }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="block">
          <el-pagination
            :current-page="page.pageNum"
            :page-sizes="[5, 10]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getstaffId } from '@/views/mobileView/utlis'
import { getFeedbackListRequest } from '@/api/mobile.js'
export default {
  data() {
    return {
      staffId: getstaffId(),
      dialogVisible: false,
      page: {
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      totalCount: 0
    }
  },
  computed: {},
  mounted() {
    console.log(process.env.VUE_APP_ENV)
  },
  methods: {
    init() {
      this.getList()
    },
    show() {
      this.init()
      this.dialogVisible = true
    },
    async getList() {
      const res = await getFeedbackListRequest({
        page: this.page,
        filter: {
          userCode: this.staffId
        }
      })
      if (res.code !== 200) return this.$message.error({ message: '获取反馈列表失败', duration: 3000 })
      this.list = res.data.dataList
      this.totalCount = res.data.totalCount
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.pageNum = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.rank-container {
  padding: 0 10px;
}
.block {
  margin-top: 20px;
  text-align: center;

  .demonstration {
    display: none; // 隐藏"页数较少时的效果"文本
  }
}
.image-preview-wrapper {
  position: relative;
  display: inline-block;
}
.image-count {
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
}
</style>
