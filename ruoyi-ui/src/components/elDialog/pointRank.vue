<template>
  <div>
    <!-- 触发弹窗的按钮 -->
    <el-button type="text" @click="dialogVisible = true">积分排行</el-button>

    <!-- 弹窗组件 -->
    <el-dialog title="积分排行榜" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false">
      <div class="rank-container">
        <!-- 标签页 -->
        <div class="rank-tabs">
          <div
            v-for="tab in tabs"
            :key="tab.name"
            :class="['tab-item', activeTab === tab.name ? 'active' : '']"
            @click="handleTabClick(tab.name)"
          >
            {{ tab.label }}
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="divider" />

        <!-- 表头 -->
        <div class="rank-header">
          <div class="rank-column rank-number">排名</div>
          <div class="rank-column rank-orgName">单位</div>
          <div class="rank-column rank-name">姓名</div>
          <div class="rank-column rank-points">总分数</div>
        </div>

        <!-- 排行榜列表 -->
        <div v-loading="loading" class="rank-list">
          <div v-for="(item, index) in currentRankData" :key="index" class="rank-item">
            <div class="rank-column rank-number">
              <!-- 前三名显示奖牌图标 -->
              <img v-if="item.ranking === '1'" src="@/assets/images/gold-medal.png" class="medal-icon" alt="第一名">
              <img v-else-if="item.ranking === '2'" src="@/assets/images/silver-medal.png" class="medal-icon" alt="第二名">
              <img v-else-if="item.ranking === '3'" src="@/assets/images/bronze-medal.png" class="medal-icon" alt="第三名">
              <span v-else>{{ item.ranking }}</span>
            </div>
            <div class="rank-column rank-orgName">{{ item.orgName? item.orgName : '--' }} </div>
            <div class="rank-column rank-name">{{ item.userName? item.userName : '--' }}</div>
            <div class="rank-column rank-points">{{ item.points? item.points : '--' }}</div>
          </div>

          <!-- 无数据时显示 -->
          <div v-if="currentRankData.length === 0" class="no-data">
            暂无排行数据
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as pointApi from '@/api/points'
let staffId = ''
// 如果当前为开发环境
if (['development'].includes(process.env.VUE_APP_ENV)) {
  staffId = process.env.VUE_APP_DEV_STAFF_ID;
} else {
  // 测试和正式环境
  staffId = localStorage.getItem('staffId')
}
export default {
  data() {
    return {
      dialogVisible: false,
      activeTab: 'week',
      loading: false,
      tabs: [
        { name: 'yesterday', label: '今日' },
        { name: 'week', label: '本周' },
        { name: 'month', label: '本月' }
      ],
      yesterdayRank: [

      ],
      weekRank: [

      ],
      monthRank: [

      ]
    }
  },
  computed: {
    currentRankData() {
      switch (this.activeTab) {
        case 'yesterday':
          return this.yesterdayRank
        case 'week':
          return this.weekRank
        case 'month':
          return this.monthRank
        default:
          return []
      }
    }
  },
  mounted() {

  },
  methods: {
    async init() {
      this.loading = true
      const p1 = pointApi.pointsdetailsTodayRank(staffId)
      // .then(res => {
      // this.yesterdayRank = res.data
      // })
      const p2 = pointApi.pointsdetailsWeekRank(staffId)
      // .then(res => {
      // this.weekRank = res.data
      // })
      const p3 = pointApi.pointsdetailsMonthRank(staffId)
      // .then(res => {
      // this.monthRank = res.data
      // })
      Promise.all([p1, p2, p3]).then(v => {
        this.yesterdayRank = v[0].data
        this.weekRank = v[1].data
        this.monthRank = v[2].data
        this.loading = false
      }).catch(reason => {
        console.log(reason)
      })
    },
    show() {
      this.init()
      this.dialogVisible = true
    },
    handleTabClick(tabName) {
      this.activeTab = tabName
    }
  }
}
</script>

<style scoped lang="scss">
.rank-container {
  padding: 0 10px;
}

.rank-tabs {
  display: flex;
  border-bottom: none;

  .tab-item {
    padding: 10px 20px;
    cursor: pointer;
    font-size: 16px;
    color: #606266;
    position: relative;

    &.active {
      color: #67C23A;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #67C23A;
      }
    }
  }
}

.divider {
  height: 1px;
  background-color: #EBEEF5;
  margin: 0 0 15px 0;
}

.rank-header {
  display: flex;
  padding: 10px 0;
  font-weight: 500;
  color: #606266;
  border-bottom: 1px solid #EBEEF5;

}

.rank-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #EBEEF5;

  &:last-child {
    border-bottom: none;
  }
}

.rank-column {
  display: flex;
  align-items: center;

  &.rank-number {
    flex: 1;
    justify-content: center;
  }

  &.rank-orgName {
    flex: 2;
    justify-content: center;
  }

  &.rank-name {
    flex: 2;
    justify-content: center;
  }

  &.rank-points {
    flex: 1;
    justify-content: center;
  }
}

.medal-icon {
  width: 24px;
  height: 24px;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}
.rank-list{
  height: 400px;
  overflow-y: scroll;
  // 透明滚动条
  &::-webkit-scrollbar-track {
    background: #fff;
  }
}
/* 自定义关闭按钮样式 */
::v-deep .el-dialog__headerbtn .el-dialog__close {
  font-size: 20px;
}

/* 自定义对话框标题样式 */
::v-deep .el-dialog__title {
  font-size: 18px;
  font-weight: 500;
}

/* 移除对话框底部区域 */
::v-deep .el-dialog__body {
  padding-bottom: 30px;
}

::v-deep .el-dialog__header {
  padding: 15px 20px;
}

::v-deep .el-dialog {
  border-radius: 2px;
}
</style>
