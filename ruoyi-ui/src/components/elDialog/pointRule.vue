<template>
  <div>
    <!-- 弹窗组件 -->
    <el-dialog title="获取积分" :visible.sync="dialogVisible" width="50%">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="积分任务" name="form1">
          <div class="ruleBox">
            <!-- 总积分显示 -->
            <div class="total-points">
              <div class="points-title">您的总积分为：</div>
              <div class="points-value">{{ totalPoints }}分</div>
            </div>

            <!-- 积分任务列表 -->
            <div class="task-list">
              <div
                v-for="(task, index) in taskList"
                :key="index"
                class="task-item"
              >
                <div class="task-name">{{ task.pointsName }}</div>
                <div class="task-points">
                  {{ task.description }}
                  <!-- <el-tooltip effect="dark" :content="task.description" place="top" :disabled="task.pointsCode !== 'firstlogin'&&task.pointsCode !== 'dislikeuserfeedback'&&task.pointsCode !== 'userfeedback'">
                    <div class="text-ellipsis">
                      {{ task.description }}
                    </div>
                  </el-tooltip> -->
                </div>
                <div
                  v-if="task.taskStatus === -1"
                  class="task-status status-todo"
                  @click="goToHome"
                >
                  去完成
                </div>
                <div
                  v-if="task.taskStatus === 0"
                  class="task-status status-completed"
                  @click="dialogVisible = false"
                >
                  已完成
                </div>
                <div
                  v-if="task.taskStatus === 1"
                  class="task-status status-in-progress"
                  @click="dialogVisible = false"
                >
                  继续完成
                </div>
                <div
                  v-if="task.taskStatus === 2"
                  class="task-status status-in-progress"
                  @click="dialogVisible = false"
                >
                  继续完成
                </div>
                <!-- <div v-if="task.taskStatus === 2" class="task-status status-continue" @click="dialogVisible = false">持续完成</div> -->
                <div class="task-inforText">
                  <span
                    v-if="
                      task.pointsCode === 'firstlogin' && currentStreakDays > 0
                    "
                    class="progress-text"
                  >已连续登录{{ currentStreakDays }}天</span>
                  <span
                    v-else-if="task.points > 0"
                    class="progress-text"
                  >已获得{{ task.points }}分</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="积分明细" name="form2">
          <!-- 总积分显示 -->
          <div class="total-points">
            <div class="points-title">您的总积分为：</div>
            <div class="points-value">{{ totalPoints }}分</div>
          </div>
          <el-table ref="filterTable" :data="tableData" style="width: 100%">
            <el-table-column prop="pointsName" label="积分名称" />
            <el-table-column prop="pointsPerTransaction" label="积分数量" />
            <el-table-column prop="updateTime" label="时间" />
          </el-table>
          <div class="block">
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[5, 10]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import * as pointApi from '@/api/points'
let staffId = ''
// 如果当前为开发环境
if (['development'].includes(process.env.VUE_APP_ENV)) {
  staffId = process.env.VUE_APP_DEV_STAFF_ID
} else {
  // 测试和正式环境
  staffId = localStorage.getItem('staffId')
}
// console.log('staffId', staffId)
export default {
  data() {
    return {
      dialogVisible: false, // 控制弹窗的显示状态
      activeTab: 'form1', // 当前选中的标签页
      totalPoints: 0, // 总积分
      currentStreakDays: 0,
      taskList: [],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      form1Data: {
        name: ''
      },
      form2Data: {
        email: ''
      }
    }
  },
  mounted() {},
  methods: {
    init() {
      this.getUserbehavior({ staffId: staffId })
      this.tasklist({ staffId: staffId })
      this.pointsdetails({
        page: {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        },
        filter: {},
        staffId: staffId
      })
    },
    getUserbehavior(staffId) {
      pointApi.userbehavior(staffId).then((res) => {
        this.totalPoints = res.data.points
        this.currentStreakDays = res.data.currentStreakDays
      })
    },
    tasklist(staffId) {
      pointApi.tasklist(staffId).then((res) => {
        this.taskList = res.data
      })
    },
    pointsdetails(params) {
      pointApi.pointsdetails(params).then((res) => {
        this.tableData = res.data.dataList
        this.total = res.data.totalCount
      })
    },
    show() {
      this.init()
      this.activeTab = 'form1'
      this.dialogVisible = true
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.pageSize = val
      pointApi
        .pointsdetails({
          page: {
            pageNum: this.currentPage,
            pageSize: this.pageSize
          },
          staffId: staffId,
          filter: {}
        })
        .then((res) => {
          this.tableData = res.data.dataList
          this.total = res.data.totalCount
        })
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      pointApi
        .pointsdetails({
          page: {
            pageNum: this.currentPage,
            pageSize: this.pageSize
          },
          staffId: staffId,
          filter: {}
        })
        .then((res) => {
          this.tableData = res.data.dataList
          this.total = res.data.totalCount
        })
    },
    handleTabClick(tab) {
      // console.log('选中的标签页：', tab.name)
    },
    handleSubmit() {
      // 处理表单提交的逻辑
      console.log('表单1数据：', this.form1Data)
      console.log('表单2数据：', this.form2Data)
      this.dialogVisible = false // 提交后关闭弹窗
    },
    goToHome() {
      this.dialogVisible = false
      // 跳转到首页并打开新会话
      this.$router.push('/deepseek')
      // 使用 nextTick 确保路由跳转完成后再触发新会话
      this.$nextTick(() => {
        // 通过事件总线或直接调用父组件方法来触发新会话
        if (this.$parent && this.$parent.newChatClick) {
          this.$parent.newChatClick()
        } else {
          // 尝试查找根组件
          let parent = this.$parent
          while (parent && !parent.newChatClick) {
            parent = parent.$parent
          }
          if (parent && parent.newChatClick) {
            parent.newChatClick()
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.total-points {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .points-title {
    font-size: 16px;
    color: #333;
  }

  .points-value {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-left: 5px;
  }
}
.ruleBox {
  // padding: 0 20px;
  .task-list {
    max-height: 400px;
    overflow-y: scroll;
    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
      .task-name {
        font-size: 14px;
        color: #333;
        margin-bottom: 5px;
        width: 30%;
        text-align: center;
        padding: 0 10px;
      }
      .task-points {
        font-size: 12px;
        color: #666;
        width: 40%;
        padding: 0 10px;
        // .text-ellipsis {
        //   white-space: nowrap;
        //   overflow: hidden;
        //   text-overflow: ellipsis;
        //   max-width: 100%;
        //   cursor: pointer;
        // }
      }
      .task-status {
        padding: 0 10px;
        text-align: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        width: 12%;
        &.status-completed {
          background-color: #f0f9eb;
          color: #67c23a;
        }

        &.status-in-progress {
          background-color: #fdf6ec;
          color: #e6a23c;
          cursor: pointer;
        }

        &.status-todo {
          background-color: #ecf5ff;
          color: #409eff;
          cursor: pointer;
        }
        &.status-continue {
          background-color: #f0e5ff;
          color: #8e44ad;
          cursor: pointer;
        }
        .progress-text {
          display: block;
          margin-top: 3px;
          font-size: 12px;
        }
      }
      .task-inforText {
        padding: 0 10px;
        width: 18%;
        font-size: 14px;
        color: rgba(64, 64, 64, 0.6);
      }
    }
  }
}

.block {
  margin-top: 20px;
  text-align: center;

  .demonstration {
    display: none; // 隐藏"页数较少时的效果"文本
  }
}

.dialog-footer {
  text-align: center;
}
</style>
