import { customAlphabet } from 'nanoid'

const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890_', 24)
export function checkOutLinkUid() {
  const uid = localStorage.getItem('outLinkUid')
  return uid
}

export function generateChatId(size) {
  const firstChar = customAlphabet('abcdefghijklmnopqrstuvwxyz', 1)()
  if (size === 1) return firstChar
  const randomsStr = customAlphabet(
    'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
    size - 1
  )()
  return `${firstChar}${randomsStr}`
}

/**
 * 根据文件名后缀返回对应的 MIME 类型
 * @param {string} filename - 完整文件名（如 "image.jpg"）
 * @returns {string} MIME 类型，默认返回 'application/octet-stream'
 */
export function getMimeType(filename) {
  // 处理无后缀名、隐藏文件等特殊情况
  if (!filename.includes('.') || filename.startsWith('.')) {
    return 'application/octet-stream'
  }

  // 提取后缀并转为小写
  const ext = filename.split('.').pop().toLowerCase()

  // MIME 类型映射表（精简示例，实际应包含完整列表）
  const mimeMap = {
    // 图片
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    bmp: 'image/bmp',
    ico: 'image/x-icon',

    // 文档
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    odt: 'application/vnd.oasis.opendocument.text',

    // 文本
    txt: 'text/plain',
    csv: 'text/csv',
    html: 'text/html',
    htm: 'text/html',
    css: 'text/css',
    js: 'text/javascript',
    json: 'application/json',
    xml: 'application/xml',

    // 压缩文件
    zip: 'application/zip',
    rar: 'application/vnd.rar',
    '7z': 'application/x-7z-compressed',
    tar: 'application/x-tar',
    gz: 'application/gzip',

    // 音视频
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    mkv: 'video/x-matroska',

    // 编程相关
    exe: 'application/x-msdownload',
    dll: 'application/x-msdownload',
    sh: 'application/x-sh',
    php: 'application/x-httpd-php',
    py: 'text/x-python',
    java: 'text/x-java-source',

    // 其他常见类型
    ttf: 'font/ttf',
    woff: 'font/woff',
    woff2: 'font/woff2',
    eot: 'application/vnd.ms-fontobject',
    otf: 'font/otf',
    swf: 'application/x-shockwave-flash',
    apk: 'application/vnd.android.package-archive',
    ipa: 'application/octet-stream'
  }

  return mimeMap[ext] || 'application/octet-stream'
}

export function base64ToFile(base64Data, filename) {
  // 步骤1：分离 MIME 类型和 base64 数据
  const arr = base64Data.split(',')
  const mimeMatch = arr[0].match(/:(.*?);/)
  const mimeType = mimeMatch ? mimeMatch[1] : 'application/octet-stream'
  const b64Data = arr[1] || arr[0] // 处理无头部的 base64

  // 步骤2：解码 Base64 → 二进制
  const byteChars = atob(b64Data)
  const byteNumbers = new Array(byteChars.length)

  for (let i = 0; i < byteChars.length; i++) {
    byteNumbers[i] = byteChars.charCodeAt(i)
  }

  // 步骤3：创建 Blob
  const byteArray = new Uint8Array(byteNumbers)
  const blob = new Blob([byteArray], { type: mimeType })

  // 步骤4：Blob → File
  return new File([blob], filename, {
    type: mimeType,
    lastModified: Date.now()
  })
}

export function getUrlParam(url, name) {
  var pattern = new RegExp('[?&]' + name + '=([^&]+)', 'g')
  var matcher = pattern.exec(url)
  var items = null
  if (matcher !== null) {
    try {
      items = decodeURIComponent(decodeURIComponent(matcher[1]))
    } catch (e) {
      try {
        items = decodeURIComponent(matcher[1])
      } catch (e) {
        items = matcher[1]
      }
    }
  }
  return items
}
export function fileSvgIcon(fileName, w, h) {
  const fileType = fileName.split('.').pop().toLowerCase()
  let icon = '--'
  if (['docx', 'doc'].includes(fileType)) {
    icon = `<svg width="${w}" height="${h}" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_455_21)">
                <path d="M21.4257 0H24.2124V3.81362C29.1976 3.85701 34.1779 3.76058 39.1535 3.85701C39.3988 3.82763 39.6476 3.85477 39.8808 3.93634C40.1141 4.01792 40.3256 4.15176 40.4991 4.32764C40.6727 4.50352 40.8037 4.71677 40.8821 4.95107C40.9606 5.18537 40.9844 5.43452 40.9518 5.67944C41.0337 15.0713 40.9518 24.4824 41 33.8597C40.9518 34.824 41.0916 35.8895 40.5468 36.7525C39.867 37.2346 38.9895 37.1864 38.194 37.2346H24.2124V40.9807H21.3196C14.2131 39.6742 7.10654 38.4592 0 37.1864V3.82326C7.14029 2.54563 14.2854 1.30174 21.4257 0Z" fill="#2A5699"/>
                <path d="M24.2124 5.24553H39.5826V35.7545H24.2124V31.9408H36.3234V30.0123H24.2124V27.6499H36.3234V25.7214H24.2124V23.359H36.3234V21.4305H24.2124V19.0199H36.3234V17.1637H24.2124V14.7531H36.3234V12.8728H24.2124V10.4862H36.3234V8.58184H24.2124V5.24553ZM9.64256 13.6683C10.5297 13.6152 11.412 13.5767 12.2991 13.5285C12.921 16.7491 13.5526 19.96 14.2276 23.171C14.7531 19.8588 15.3365 16.561 15.9006 13.2585C16.8311 13.2247 17.7616 13.1717 18.6921 13.1138C17.6362 17.7326 16.7154 22.3948 15.5631 26.9846C14.7868 27.3992 13.6346 26.9846 12.6992 27.0328C12.0532 23.8701 11.3348 20.7314 10.7804 17.5638C10.2356 20.6446 9.52685 23.6965 8.90491 26.7532C8.00815 26.705 7.1114 26.6519 6.20983 26.5892C5.43842 22.3947 4.5272 18.2244 3.79919 14.0202C4.5947 13.9817 5.39503 13.9479 6.20983 13.919C6.69195 16.9564 7.23675 19.9793 7.6562 23.0215C8.30225 19.9022 8.97241 16.7876 9.64256 13.6683Z" fill="white"/>
                </g>
                <defs>
                <clipPath id="clip0_455_21">
                <rect width="41" height="41" fill="white"/>
                </clipPath>
                </defs>
                </svg>
                `
  } else if (['pdf'].includes(fileType)) {
    icon = `<svg width="${w}" height="${h}" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_72_821)">
                <path d="M20.9062 0H23.6225V3.72175C28.4732 3.74821 33.3238 3.66883 38.2009 3.74821C38.4393 3.71984 38.6811 3.74595 38.908 3.82457C39.1349 3.90318 39.3411 4.03225 39.5109 4.20206C39.6807 4.37187 39.8098 4.57799 39.8884 4.8049C39.967 5.03181 39.9931 5.27361 39.9647 5.51207C40.0397 14.6753 39.9647 23.843 40.0088 33.0063C39.9647 33.9455 40.097 34.9862 39.5679 35.8196C38.902 36.3047 38.0465 36.2606 37.2704 36.2606H23.6314V39.9824H20.7827C13.864 38.708 6.93198 37.5218 0 36.2783V3.73057C6.96726 2.48264 13.9389 1.26998 20.9062 0Z" fill="#A33639"/>
                <path d="M23.6226 5.1152H38.6154V34.8848H23.6226V31.163H35.436V29.3022H23.6226V26.9783H35.436V25.135H23.6226V22.7891H35.436V20.9282H23.6226V18.6043H35.436V16.7567H23.6226V14.4196H35.436V12.5587H23.6226V10.2304H35.436V33.5575H23.6226V5.1152Z" fill="white"/>
                <path d="M25.2145 20.6901H37.0279V22.551H25.2145V20.6901ZM25.2145 23.8871H37.0279V25.748H25.2145V23.8871ZM25.2145 27.0841H37.0279V28.945H25.2145V27.0841ZM23.2963 30.2811H37.0279V32.142H23.2963V30.2811Z" fill="#A33639"/>
                <path d="M7.05549 12.3956C9.37056 12.5058 12.1707 11.4563 14.1109 13.229C15.9454 15.5132 15.4603 19.7068 12.7395 21.0693C11.7738 21.5765 10.6626 21.5103 9.61309 21.4706V26.5858L7.05549 26.3698C7.02021 21.7132 7.01139 17.0522 7.05549 12.3956Z" fill="white"/>
                <path d="M9.59537 14.7547C10.4332 14.715 11.4739 14.5563 12.0383 15.3632C12.268 15.8035 12.3926 16.291 12.4026 16.7875C12.4125 17.2839 12.3075 17.776 12.0956 18.2251C11.6106 19.107 10.5126 19.0365 9.66151 19.1379C9.57332 17.6783 9.58214 16.2187 9.59537 14.7547ZM34.5981 14.8385C33.9035 14.7895 33.2425 14.5208 32.7108 14.0712C31.6728 14.3043 30.6576 14.6289 29.6769 15.0413C28.8832 16.448 28.1468 17.1668 27.5074 17.1668C27.3723 17.1719 27.2387 17.1366 27.1238 17.0654C26.9916 17.0044 26.8797 16.9068 26.8015 16.784C26.7233 16.6613 26.6821 16.5186 26.6828 16.3731C26.6828 16.1438 26.7357 15.4911 29.1566 14.4549C29.707 13.4416 30.1603 12.3785 30.5104 11.2799C30.2017 10.667 29.5402 9.15885 29.9988 8.39157C30.0758 8.25339 30.1916 8.14086 30.3319 8.06795C30.4722 7.99504 30.6309 7.96497 30.7882 7.98147C30.9169 7.98295 31.0436 8.01346 31.1589 8.07072C31.2742 8.12798 31.3751 8.21053 31.454 8.3122C31.7848 8.75316 31.7583 9.74534 31.3262 11.1785C31.7368 11.9472 32.2751 12.6406 32.918 13.229C33.4464 13.1202 33.9838 13.0611 34.5232 13.0526C35.7226 13.079 35.9034 13.6391 35.8769 13.9742C35.8769 14.8561 35.0347 14.8561 34.6025 14.8561L34.5981 14.8385ZM27.3707 16.4833L27.4589 16.4612C27.8348 16.352 28.1637 16.1209 28.3937 15.8042C27.9953 15.9291 27.6405 16.1646 27.3707 16.4833ZM31.0175 8.66056H30.9337C30.895 8.65667 30.8562 8.66599 30.8235 8.68702C30.729 9.13385 30.7864 9.59943 30.9866 10.0099C31.1465 9.57605 31.1574 9.10129 31.0175 8.66056ZM30.8675 12.4044V12.4529L30.8411 12.4264C30.6294 12.9864 30.4001 13.5376 30.1355 14.08L30.1796 14.0536V14.1109C30.6924 13.9146 31.2167 13.7496 31.7495 13.617L31.723 13.595H31.7936C31.4465 13.2295 31.1363 12.8307 30.8675 12.4044ZM34.5628 13.7625C34.3243 13.7517 34.0853 13.7739 33.8529 13.8287C34.1039 13.9745 34.3839 14.0633 34.6731 14.0888C34.8561 14.118 35.0437 14.0967 35.2155 14.0271C35.1978 13.9257 35.0876 13.7625 34.5452 13.7625H34.5628Z" fill="#A33639"/>
                </g>
                <defs>
                <clipPath id="clip0_72_821">
                <rect width="40" height="40" fill="white"/>
                </clipPath>
                </defs>
                </svg>
              `
  } else if (['xlsx', 'xls'].includes(fileType)) {
    icon = `<svg width="${w}" height="${h}" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M39.732 5.54401H22.092V36.414H39.732C40.362 36.414 40.824 35.91 40.824 35.322V6.67801C40.824 6.04801 40.362 5.54401 39.732 5.54401Z" fill="#4CAF50"/>
                <path d="M29.82 11.088H37.548V14.406H29.82V11.088ZM29.82 22.092H37.548V25.41H29.82V22.092ZM29.82 27.594H37.548V30.912H29.82V27.594ZM29.82 16.59H37.548V19.908H29.82V16.59ZM22.092 11.088H27.594V14.406H22.092V11.088ZM22.092 22.092H27.594V25.41H22.092V22.092ZM22.092 27.594H27.594V30.912H22.092V27.594ZM22.092 16.59H27.594V19.908H22.092V16.59Z" fill="white"/>
                <path d="M24.318 40.866L1.17603 36.456V5.544L24.318 1.134V40.866Z" fill="#2E7D32"/>
                <path d="M15.5821 28.56L12.9781 23.646C12.8941 23.478 12.7681 23.142 12.6841 22.638H12.6001C12.5581 22.89 12.4321 23.226 12.2641 23.688L9.66006 28.56H5.58606L10.4161 21L6.00606 13.44H10.1641L12.3481 17.976C12.5161 18.354 12.6841 18.774 12.8101 19.236H12.8521C12.9361 18.942 13.1041 18.522 13.3141 17.934L15.6661 13.44H19.4461L14.9521 20.916L19.6141 28.56H15.5821Z" fill="white"/>
                </svg>
                `
  } else if (['pptx', 'ppt'].includes(fileType)) {
    icon = `
                <svg width="${w}" height="${h}" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_756_383)">
                <path d="M21.0442 0H23.6218V4.18626C28.4807 4.21449 33.3443 4.13452 38.2032 4.18626C38.4448 4.15797 38.6896 4.18547 38.9189 4.26666C39.1481 4.34785 39.3557 4.48055 39.5256 4.65457C39.6956 4.82858 39.8233 5.03926 39.899 5.27039C39.9747 5.50152 39.9964 5.74695 39.9624 5.98777C40.0423 14.6754 39.9624 23.3678 40.0094 32.0602C39.9624 33.0009 40.0988 34.0405 39.572 34.8824C38.9087 35.3528 38.048 35.3104 37.2672 35.3528C32.7234 35.3293 28.1797 35.3528 23.6265 35.3528V39.9812H20.8043C13.8711 38.7065 6.93321 37.5259 0 36.2794V3.73001C7.01317 2.48824 14.0263 1.22295 21.0442 0Z" fill="#D24625"/>
                <path d="M23.6218 5.58325H38.6171V33.9511H23.6218V30.2305H34.5296V28.3725H23.6218V26.0207H34.5296V24.1863H23.6265V21.4534C25.428 22.0226 27.4788 22.0085 29.0875 20.9031C30.8278 19.8448 31.7357 17.8316 31.8862 15.8231H25.9031C25.9031 13.8006 25.9266 11.778 25.8655 9.76011C25.1176 9.90592 24.3744 10.0658 23.6312 10.2305L23.6218 5.58325Z" fill="white"/>
                <path d="M26.8109 8.77704C28.3813 8.8939 29.8553 9.57934 30.9567 10.705C32.0581 11.8306 32.7113 13.3192 32.7939 14.8918C30.7996 14.9153 28.8052 14.8918 26.8062 14.8918C26.8109 12.8457 26.8109 10.8184 26.8109 8.77704Z" fill="#D24625"/>
                <path d="M7.29069 12.286C9.76012 12.4083 12.7469 11.2888 14.8166 13.1797C16.7733 15.6162 16.2559 20.0894 13.3537 21.5428C12.3236 22.0837 11.1383 22.0132 10.0188 21.9708V27.4271L7.31421 27.1966C7.24366 22.2295 7.23425 17.2578 7.29069 12.286Z" fill="white"/>
                <path d="M9.99057 14.8025C10.8843 14.7648 11.9943 14.5955 12.5964 15.4563C12.8414 15.9259 12.9743 16.4459 12.985 16.9754C12.9956 17.505 12.8835 18.0299 12.6576 18.5089C12.1402 19.4497 10.9689 19.3697 10.0611 19.4826C9.96706 17.9257 9.97646 16.3688 9.99057 14.8025Z" fill="#D24625"/>
                </g>
                <defs>
                <clipPath id="clip0_756_383">
                <rect width="40" height="40" fill="white"/>
                </clipPath>
                </defs>
                </svg>`
  } else if (['txt'].includes(fileType)) {
    icon = `<svg width="${w}" height="${h}" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M39.732 5.54401H22.092V36.414H39.732C40.362 36.414 40.824 35.91 40.824 35.322V6.67801C40.824 6.04801 40.362 5.54401 39.732 5.54401Z" fill="#4CAF50"/>
                <path d="M29.82 11.088H37.548V14.406H29.82V11.088ZM29.82 22.092H37.548V25.41H29.82V22.092ZM29.82 27.594H37.548V30.912H29.82V27.594ZM29.82 16.59H37.548V19.908H29.82V16.59ZM22.092 11.088H27.594V14.406H22.092V11.088ZM22.092 22.092H27.594V25.41H22.092V22.092ZM22.092 27.594H27.594V30.912H22.092V27.594ZM22.092 16.59H27.594V19.908H22.092V16.59Z" fill="white"/>
                <path d="M24.318 40.866L1.17603 36.456V5.544L24.318 1.134V40.866Z" fill="#2E7D32"/>
                <path d="M17.5 14.5H6V17H10.5V30.5H13V17H17.5V14.5Z" fill="white" stroke="white"/>
                </svg>
                `
  } else {
    icon = `<svg width="${w}" height="${h}" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4064 0H10.6298V1.67506C12.8184 1.69412 15.0049 1.65176 17.1893 1.69412C17.297 1.68122 17.4063 1.69314 17.5087 1.72896C17.6111 1.76479 17.7039 1.82358 17.7801 1.90083C17.8563 1.97809 17.9138 2.07175 17.9483 2.17466C17.9827 2.27758 17.9932 2.38701 17.9788 2.49459C18.0148 6.61976 17.9788 10.7534 18 14.8722C17.9788 15.2958 18.0402 15.7638 17.801 16.1428C17.5026 16.3546 17.1174 16.3334 16.7681 16.3546H10.6298V18H9.35983C6.23989 17.4261 3.11994 16.8925 0 16.3334V1.67929C3.13476 1.11812 6.27164 0.571765 9.4064 0Z" fill="#2A5699"/>
                <path d="M10.6289 2.30469H17.3768V15.7052H10.6289V14.0301H15.9459V13.183H10.6289V12.1454H15.9459V11.2983H10.6289V10.2607H15.9459V9.41363H10.6289V8.35481H15.9459V7.53951H10.6289V6.48069H15.9459V5.65481H10.6289V4.60657H15.9459V3.7701H10.6289V2.30469Z" fill="white"/>
                <path d="M5.24 10.43C5.20667 10.1633 5.22667 9.93 5.3 9.73C5.38 9.53 5.49 9.35 5.63 9.19C5.77667 9.03 5.93667 8.88333 6.11 8.75C6.29 8.61667 6.45667 8.48667 6.61 8.36C6.77 8.23333 6.9 8.10333 7 7.97C7.1 7.83667 7.15 7.69 7.15 7.53C7.15 7.33 7.1 7.16 7 7.02C6.9 6.88 6.75333 6.77333 6.56 6.7C6.37333 6.62667 6.14667 6.59 5.88 6.59C5.57333 6.59 5.29333 6.65667 5.04 6.79C4.78667 6.92333 4.53667 7.12 4.29 7.38L3.47 6.62C3.79 6.25333 4.16667 5.96 4.6 5.74C5.04 5.51333 5.52333 5.4 6.05 5.4C6.53667 5.4 6.96667 5.47333 7.34 5.62C7.72 5.76667 8.01667 5.99 8.23 6.29C8.45 6.58333 8.56 6.95333 8.56 7.4C8.56 7.64667 8.50667 7.86333 8.4 8.05C8.3 8.23 8.16667 8.39333 8 8.54C7.84 8.68 7.67 8.81667 7.49 8.95C7.31 9.07667 7.14333 9.21 6.99 9.35C6.83667 9.49 6.71667 9.64667 6.63 9.82C6.54333 9.99333 6.51 10.1967 6.53 10.43H5.24ZM5.89 13.09C5.63667 13.09 5.42667 13.0067 5.26 12.84C5.09333 12.6733 5.01 12.4633 5.01 12.21C5.01 11.95 5.09333 11.7367 5.26 11.57C5.42667 11.4033 5.63667 11.32 5.89 11.32C6.14333 11.32 6.35333 11.4033 6.52 11.57C6.68667 11.7367 6.77 11.95 6.77 12.21C6.77 12.4633 6.68667 12.6733 6.52 12.84C6.35333 13.0067 6.14333 13.09 5.89 13.09Z" fill="white"/>
                </svg>
                `
  }
  return icon
}
export function fileSvgIconList(fileNameList, w, h) {
  let icon = ''
  console.log('fileSvgIconList')
  const fileTypelist = []
  fileNameList.forEach(i => {
    const fileType = i.split('.').pop().toLowerCase()
    fileTypelist.push(fileType)
  })
  const uniqueArr = [...new Set(fileTypelist)]
  uniqueArr.forEach(fileType => {
    if (['docx', 'doc'].includes(fileType)) {
      icon += `<svg width="${w}" height="${h}" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_455_21)">
                <path d="M21.4257 0H24.2124V3.81362C29.1976 3.85701 34.1779 3.76058 39.1535 3.85701C39.3988 3.82763 39.6476 3.85477 39.8808 3.93634C40.1141 4.01792 40.3256 4.15176 40.4991 4.32764C40.6727 4.50352 40.8037 4.71677 40.8821 4.95107C40.9606 5.18537 40.9844 5.43452 40.9518 5.67944C41.0337 15.0713 40.9518 24.4824 41 33.8597C40.9518 34.824 41.0916 35.8895 40.5468 36.7525C39.867 37.2346 38.9895 37.1864 38.194 37.2346H24.2124V40.9807H21.3196C14.2131 39.6742 7.10654 38.4592 0 37.1864V3.82326C7.14029 2.54563 14.2854 1.30174 21.4257 0Z" fill="#2A5699"/>
                <path d="M24.2124 5.24553H39.5826V35.7545H24.2124V31.9408H36.3234V30.0123H24.2124V27.6499H36.3234V25.7214H24.2124V23.359H36.3234V21.4305H24.2124V19.0199H36.3234V17.1637H24.2124V14.7531H36.3234V12.8728H24.2124V10.4862H36.3234V8.58184H24.2124V5.24553ZM9.64256 13.6683C10.5297 13.6152 11.412 13.5767 12.2991 13.5285C12.921 16.7491 13.5526 19.96 14.2276 23.171C14.7531 19.8588 15.3365 16.561 15.9006 13.2585C16.8311 13.2247 17.7616 13.1717 18.6921 13.1138C17.6362 17.7326 16.7154 22.3948 15.5631 26.9846C14.7868 27.3992 13.6346 26.9846 12.6992 27.0328C12.0532 23.8701 11.3348 20.7314 10.7804 17.5638C10.2356 20.6446 9.52685 23.6965 8.90491 26.7532C8.00815 26.705 7.1114 26.6519 6.20983 26.5892C5.43842 22.3947 4.5272 18.2244 3.79919 14.0202C4.5947 13.9817 5.39503 13.9479 6.20983 13.919C6.69195 16.9564 7.23675 19.9793 7.6562 23.0215C8.30225 19.9022 8.97241 16.7876 9.64256 13.6683Z" fill="white"/>
                </g>
                <defs>
                <clipPath id="clip0_455_21">
                <rect width="41" height="41" fill="white"/>
                </clipPath>
                </defs>
                </svg>
                `
    } else if (['pdf'].includes(fileType)) {
      icon += `<svg width="${w}" height="${h}" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_72_821)">
                <path d="M20.9062 0H23.6225V3.72175C28.4732 3.74821 33.3238 3.66883 38.2009 3.74821C38.4393 3.71984 38.6811 3.74595 38.908 3.82457C39.1349 3.90318 39.3411 4.03225 39.5109 4.20206C39.6807 4.37187 39.8098 4.57799 39.8884 4.8049C39.967 5.03181 39.9931 5.27361 39.9647 5.51207C40.0397 14.6753 39.9647 23.843 40.0088 33.0063C39.9647 33.9455 40.097 34.9862 39.5679 35.8196C38.902 36.3047 38.0465 36.2606 37.2704 36.2606H23.6314V39.9824H20.7827C13.864 38.708 6.93198 37.5218 0 36.2783V3.73057C6.96726 2.48264 13.9389 1.26998 20.9062 0Z" fill="#A33639"/>
                <path d="M23.6226 5.1152H38.6154V34.8848H23.6226V31.163H35.436V29.3022H23.6226V26.9783H35.436V25.135H23.6226V22.7891H35.436V20.9282H23.6226V18.6043H35.436V16.7567H23.6226V14.4196H35.436V12.5587H23.6226V10.2304H35.436V33.5575H23.6226V5.1152Z" fill="white"/>
                <path d="M25.2145 20.6901H37.0279V22.551H25.2145V20.6901ZM25.2145 23.8871H37.0279V25.748H25.2145V23.8871ZM25.2145 27.0841H37.0279V28.945H25.2145V27.0841ZM23.2963 30.2811H37.0279V32.142H23.2963V30.2811Z" fill="#A33639"/>
                <path d="M7.05549 12.3956C9.37056 12.5058 12.1707 11.4563 14.1109 13.229C15.9454 15.5132 15.4603 19.7068 12.7395 21.0693C11.7738 21.5765 10.6626 21.5103 9.61309 21.4706V26.5858L7.05549 26.3698C7.02021 21.7132 7.01139 17.0522 7.05549 12.3956Z" fill="white"/>
                <path d="M9.59537 14.7547C10.4332 14.715 11.4739 14.5563 12.0383 15.3632C12.268 15.8035 12.3926 16.291 12.4026 16.7875C12.4125 17.2839 12.3075 17.776 12.0956 18.2251C11.6106 19.107 10.5126 19.0365 9.66151 19.1379C9.57332 17.6783 9.58214 16.2187 9.59537 14.7547ZM34.5981 14.8385C33.9035 14.7895 33.2425 14.5208 32.7108 14.0712C31.6728 14.3043 30.6576 14.6289 29.6769 15.0413C28.8832 16.448 28.1468 17.1668 27.5074 17.1668C27.3723 17.1719 27.2387 17.1366 27.1238 17.0654C26.9916 17.0044 26.8797 16.9068 26.8015 16.784C26.7233 16.6613 26.6821 16.5186 26.6828 16.3731C26.6828 16.1438 26.7357 15.4911 29.1566 14.4549C29.707 13.4416 30.1603 12.3785 30.5104 11.2799C30.2017 10.667 29.5402 9.15885 29.9988 8.39157C30.0758 8.25339 30.1916 8.14086 30.3319 8.06795C30.4722 7.99504 30.6309 7.96497 30.7882 7.98147C30.9169 7.98295 31.0436 8.01346 31.1589 8.07072C31.2742 8.12798 31.3751 8.21053 31.454 8.3122C31.7848 8.75316 31.7583 9.74534 31.3262 11.1785C31.7368 11.9472 32.2751 12.6406 32.918 13.229C33.4464 13.1202 33.9838 13.0611 34.5232 13.0526C35.7226 13.079 35.9034 13.6391 35.8769 13.9742C35.8769 14.8561 35.0347 14.8561 34.6025 14.8561L34.5981 14.8385ZM27.3707 16.4833L27.4589 16.4612C27.8348 16.352 28.1637 16.1209 28.3937 15.8042C27.9953 15.9291 27.6405 16.1646 27.3707 16.4833ZM31.0175 8.66056H30.9337C30.895 8.65667 30.8562 8.66599 30.8235 8.68702C30.729 9.13385 30.7864 9.59943 30.9866 10.0099C31.1465 9.57605 31.1574 9.10129 31.0175 8.66056ZM30.8675 12.4044V12.4529L30.8411 12.4264C30.6294 12.9864 30.4001 13.5376 30.1355 14.08L30.1796 14.0536V14.1109C30.6924 13.9146 31.2167 13.7496 31.7495 13.617L31.723 13.595H31.7936C31.4465 13.2295 31.1363 12.8307 30.8675 12.4044ZM34.5628 13.7625C34.3243 13.7517 34.0853 13.7739 33.8529 13.8287C34.1039 13.9745 34.3839 14.0633 34.6731 14.0888C34.8561 14.118 35.0437 14.0967 35.2155 14.0271C35.1978 13.9257 35.0876 13.7625 34.5452 13.7625H34.5628Z" fill="#A33639"/>
                </g>
                <defs>
                <clipPath id="clip0_72_821">
                <rect width="40" height="40" fill="white"/>
                </clipPath>
                </defs>
                </svg>
              `
    } else if (['xlsx', 'xls'].includes(fileType)) {
      icon += `<svg width="${w}" height="${h}" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M39.732 5.54401H22.092V36.414H39.732C40.362 36.414 40.824 35.91 40.824 35.322V6.67801C40.824 6.04801 40.362 5.54401 39.732 5.54401Z" fill="#4CAF50"/>
                <path d="M29.82 11.088H37.548V14.406H29.82V11.088ZM29.82 22.092H37.548V25.41H29.82V22.092ZM29.82 27.594H37.548V30.912H29.82V27.594ZM29.82 16.59H37.548V19.908H29.82V16.59ZM22.092 11.088H27.594V14.406H22.092V11.088ZM22.092 22.092H27.594V25.41H22.092V22.092ZM22.092 27.594H27.594V30.912H22.092V27.594ZM22.092 16.59H27.594V19.908H22.092V16.59Z" fill="white"/>
                <path d="M24.318 40.866L1.17603 36.456V5.544L24.318 1.134V40.866Z" fill="#2E7D32"/>
                <path d="M15.5821 28.56L12.9781 23.646C12.8941 23.478 12.7681 23.142 12.6841 22.638H12.6001C12.5581 22.89 12.4321 23.226 12.2641 23.688L9.66006 28.56H5.58606L10.4161 21L6.00606 13.44H10.1641L12.3481 17.976C12.5161 18.354 12.6841 18.774 12.8101 19.236H12.8521C12.9361 18.942 13.1041 18.522 13.3141 17.934L15.6661 13.44H19.4461L14.9521 20.916L19.6141 28.56H15.5821Z" fill="white"/>
                </svg>
                `
    } else if (['pptx', 'ppt'].includes(fileType)) {
      icon += `
                <svg width="${w}" height="${h}" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_756_383)">
                <path d="M21.0442 0H23.6218V4.18626C28.4807 4.21449 33.3443 4.13452 38.2032 4.18626C38.4448 4.15797 38.6896 4.18547 38.9189 4.26666C39.1481 4.34785 39.3557 4.48055 39.5256 4.65457C39.6956 4.82858 39.8233 5.03926 39.899 5.27039C39.9747 5.50152 39.9964 5.74695 39.9624 5.98777C40.0423 14.6754 39.9624 23.3678 40.0094 32.0602C39.9624 33.0009 40.0988 34.0405 39.572 34.8824C38.9087 35.3528 38.048 35.3104 37.2672 35.3528C32.7234 35.3293 28.1797 35.3528 23.6265 35.3528V39.9812H20.8043C13.8711 38.7065 6.93321 37.5259 0 36.2794V3.73001C7.01317 2.48824 14.0263 1.22295 21.0442 0Z" fill="#D24625"/>
                <path d="M23.6218 5.58325H38.6171V33.9511H23.6218V30.2305H34.5296V28.3725H23.6218V26.0207H34.5296V24.1863H23.6265V21.4534C25.428 22.0226 27.4788 22.0085 29.0875 20.9031C30.8278 19.8448 31.7357 17.8316 31.8862 15.8231H25.9031C25.9031 13.8006 25.9266 11.778 25.8655 9.76011C25.1176 9.90592 24.3744 10.0658 23.6312 10.2305L23.6218 5.58325Z" fill="white"/>
                <path d="M26.8109 8.77704C28.3813 8.8939 29.8553 9.57934 30.9567 10.705C32.0581 11.8306 32.7113 13.3192 32.7939 14.8918C30.7996 14.9153 28.8052 14.8918 26.8062 14.8918C26.8109 12.8457 26.8109 10.8184 26.8109 8.77704Z" fill="#D24625"/>
                <path d="M7.29069 12.286C9.76012 12.4083 12.7469 11.2888 14.8166 13.1797C16.7733 15.6162 16.2559 20.0894 13.3537 21.5428C12.3236 22.0837 11.1383 22.0132 10.0188 21.9708V27.4271L7.31421 27.1966C7.24366 22.2295 7.23425 17.2578 7.29069 12.286Z" fill="white"/>
                <path d="M9.99057 14.8025C10.8843 14.7648 11.9943 14.5955 12.5964 15.4563C12.8414 15.9259 12.9743 16.4459 12.985 16.9754C12.9956 17.505 12.8835 18.0299 12.6576 18.5089C12.1402 19.4497 10.9689 19.3697 10.0611 19.4826C9.96706 17.9257 9.97646 16.3688 9.99057 14.8025Z" fill="#D24625"/>
                </g>
                <defs>
                <clipPath id="clip0_756_383">
                <rect width="40" height="40" fill="white"/>
                </clipPath>
                </defs>
                </svg>`
    } else if (['txt'].includes(fileType)) {
      icon += `<svg width="${w}" height="${h}" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M39.732 5.54401H22.092V36.414H39.732C40.362 36.414 40.824 35.91 40.824 35.322V6.67801C40.824 6.04801 40.362 5.54401 39.732 5.54401Z" fill="#4CAF50"/>
                <path d="M29.82 11.088H37.548V14.406H29.82V11.088ZM29.82 22.092H37.548V25.41H29.82V22.092ZM29.82 27.594H37.548V30.912H29.82V27.594ZM29.82 16.59H37.548V19.908H29.82V16.59ZM22.092 11.088H27.594V14.406H22.092V11.088ZM22.092 22.092H27.594V25.41H22.092V22.092ZM22.092 27.594H27.594V30.912H22.092V27.594ZM22.092 16.59H27.594V19.908H22.092V16.59Z" fill="white"/>
                <path d="M24.318 40.866L1.17603 36.456V5.544L24.318 1.134V40.866Z" fill="#2E7D32"/>
                <path d="M17.5 14.5H6V17H10.5V30.5H13V17H17.5V14.5Z" fill="white" stroke="white"/>
                </svg>
                `
    } else {
      icon += `<svg width="${w}" height="${h}" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4064 0H10.6298V1.67506C12.8184 1.69412 15.0049 1.65176 17.1893 1.69412C17.297 1.68122 17.4063 1.69314 17.5087 1.72896C17.6111 1.76479 17.7039 1.82358 17.7801 1.90083C17.8563 1.97809 17.9138 2.07175 17.9483 2.17466C17.9827 2.27758 17.9932 2.38701 17.9788 2.49459C18.0148 6.61976 17.9788 10.7534 18 14.8722C17.9788 15.2958 18.0402 15.7638 17.801 16.1428C17.5026 16.3546 17.1174 16.3334 16.7681 16.3546H10.6298V18H9.35983C6.23989 17.4261 3.11994 16.8925 0 16.3334V1.67929C3.13476 1.11812 6.27164 0.571765 9.4064 0Z" fill="#2A5699"/>
                <path d="M10.6289 2.30469H17.3768V15.7052H10.6289V14.0301H15.9459V13.183H10.6289V12.1454H15.9459V11.2983H10.6289V10.2607H15.9459V9.41363H10.6289V8.35481H15.9459V7.53951H10.6289V6.48069H15.9459V5.65481H10.6289V4.60657H15.9459V3.7701H10.6289V2.30469Z" fill="white"/>
                <path d="M5.24 10.43C5.20667 10.1633 5.22667 9.93 5.3 9.73C5.38 9.53 5.49 9.35 5.63 9.19C5.77667 9.03 5.93667 8.88333 6.11 8.75C6.29 8.61667 6.45667 8.48667 6.61 8.36C6.77 8.23333 6.9 8.10333 7 7.97C7.1 7.83667 7.15 7.69 7.15 7.53C7.15 7.33 7.1 7.16 7 7.02C6.9 6.88 6.75333 6.77333 6.56 6.7C6.37333 6.62667 6.14667 6.59 5.88 6.59C5.57333 6.59 5.29333 6.65667 5.04 6.79C4.78667 6.92333 4.53667 7.12 4.29 7.38L3.47 6.62C3.79 6.25333 4.16667 5.96 4.6 5.74C5.04 5.51333 5.52333 5.4 6.05 5.4C6.53667 5.4 6.96667 5.47333 7.34 5.62C7.72 5.76667 8.01667 5.99 8.23 6.29C8.45 6.58333 8.56 6.95333 8.56 7.4C8.56 7.64667 8.50667 7.86333 8.4 8.05C8.3 8.23 8.16667 8.39333 8 8.54C7.84 8.68 7.67 8.81667 7.49 8.95C7.31 9.07667 7.14333 9.21 6.99 9.35C6.83667 9.49 6.71667 9.64667 6.63 9.82C6.54333 9.99333 6.51 10.1967 6.53 10.43H5.24ZM5.89 13.09C5.63667 13.09 5.42667 13.0067 5.26 12.84C5.09333 12.6733 5.01 12.4633 5.01 12.21C5.01 11.95 5.09333 11.7367 5.26 11.57C5.42667 11.4033 5.63667 11.32 5.89 11.32C6.14333 11.32 6.35333 11.4033 6.52 11.57C6.68667 11.7367 6.77 11.95 6.77 12.21C6.77 12.4633 6.68667 12.6733 6.52 12.84C6.35333 13.0067 6.14333 13.09 5.89 13.09Z" fill="white"/>
                </svg>
                `
    }
  })
  return icon
}
/**
 * 将文件名中的时间戳转换为日期格式
 * @param {string} filename - 原始文件名
 * @return {string} 格式化后的文件名
 */
export function formatFilenameWithTimestamp(filename) {
  // 匹配文件名中的时间戳模式（13位数字）
  const timestampRegex = /(.*)-(\d{13})\.(.*)/
  const match = filename.match(timestampRegex)

  if (match) {
    const name = match[1]
    const timestamp = match[2]
    const extension = match[3]

    // 将13位时间戳转换为日期
    const date = new Date(parseInt(timestamp))

    // 格式化日期为 YYYY-MM-DD
    const formattedDate =
          date.getFullYear() +
          '年' +
          String(date.getMonth() + 1).padStart(2, '0') +
          '月' +
          String(date.getDate()).padStart(2, '0') +
          '日'

    // 返回格式化后的文件名
    return `${name}-${formattedDate}.${extension}`
  }

  // 如果不匹配时间戳模式，返回原始文件名
  return filename
}
