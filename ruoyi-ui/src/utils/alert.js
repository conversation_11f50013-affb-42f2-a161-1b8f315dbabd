import { MessageBox } from 'element-ui'
import { getNotifyList, qaversionusageContent } from '@/api/deepSeekApi'

export const alertTest = async(disabledTrigger) => {
  const res = await getNotifyList()
  if (res && res.code === 200) {
    const visitFrom = localStorage.getItem('visitFrom')

    const isDTY = visitFrom === 'DTY' || visitFrom === 'MOBILE'
    const info = res.data.filter(item => (isDTY ? item.noticeClient === 'DTY' : item.noticeClient !== 'DTY'))

    // console.log('info', info)

    const notify = info[0]

    if (!notify) return
    const start = new Date(notify.noticeStartTime).getTime()
    const end = new Date(notify.noticeEndTime).getTime()
    const now = new Date().getTime()
    if (now >= start && now <= end) {
      MessageBox.alert(`${notify.noticeContent}`, `${notify.noticeTitle}`, {
        dangerouslyUseHTMLString: true,
        showClose: notify.canClosed === 1,
        showConfirmButton: false,
        customClass: disabledTrigger ? 'mobile-message-box message-box-max-height' : 'message-box-max-height',
        callback: () => {
        }
      })
    }
  }
}
export const qaversionusageContentAlert = async(disabledTrigger) => {
  const res = await qaversionusageContent()
  if (res && res.code === 200) {
    if (res.data === null) {
      return
    } else {
      // 不提醒
      if (res.data.reminderEnabled === 0) {
        return
        // 提醒
      } else if (res.data.reminderEnabled === 1) {
        const info = res.data
        const currentDate = new Date().toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
        MessageBox.confirm(
          `<h4>本次版本号：${info.versionNo} </h4>
      <h4>更新日期：${currentDate}</h4>
      ${info.versionContent}`,
          `更新内容说明`,

          // MessageBox.confirm(
          //   `<h4>本次版本号：V1.0.16 </h4>
          //   <h4>更新日期：${currentDate}</h4>
          //   更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！更新内容！！！！`,
          //   `更新内容说明`,
          {
            dangerouslyUseHTMLString: true,
            showClose: false,
            showCancelButton: false,
            confirmButtonText: '关闭',
            // cancelButtonText: '取消',
            customClass: disabledTrigger ? 'mobile-message-box message-box-max-height custom-message-box' : 'message-box-max-height custom-message-box',
            callback: () => {
            }
          }
        )
      }
    }
  }
}
