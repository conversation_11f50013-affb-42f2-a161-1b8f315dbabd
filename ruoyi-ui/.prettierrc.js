module.exports = {
  // 一行最多字符数
  printWidth: 100,

  // 缩进空格数
  tabWidth: 2,

  // 使用空格代替制表符
  useTabs: false,

  // 句尾是否加分号
  semi: false,

  // 是否使用单引号
  singleQuote: true,

  // 对象属性是否加引号
  quoteProps: 'as-needed',

  // JSX中使用单引号
  jsxSingleQuote: false,

  // 多行时尾逗号规则
  trailingComma: 'none',

  // 括号间是否加空格
  bracketSpacing: true,

  // JSX标签闭合位置
  jsxBracketSameLine: false,

  // 箭头函数参数括号
  arrowParens: 'always',

  // 范围格式化的文件路径忽略规则
  ignorePath: '.prettierignore',

  // 换行符格式
  endOfLine: 'lf',

  // HTML空白处格式化
  htmlWhitespaceSensitivity: 'css',

  // Vue文件脚本和样式标签缩进
  vueIndentScriptAndStyle: false,

  // 嵌入式代码格式化
  embeddedLanguageFormatting: 'auto',

  // 尝试禁用可能导致括号的格式化规则
  overrides: [
    {
      files: '*.js',
      options: {
        // 明确禁止不必要的括号（仅对部分情况有效）
        requirePragma: false,
        insertPragma: false
      }
    }
  ]
}
